-- 学生预警系统数据库初始化脚本
-- 用于创建测试数据

-- 1. 创建测试学生基本信息
INSERT INTO T_GXXS_BKSJBXX (WYBS, XH, XM, XBM, TTIMESTAMP) VALUES
('001', '202310001', '张三', '1', '20241220120000'),
('002', '202310002', '李四', '2', '20241220120000'),
('003', '202310003', '王五', '1', '20241220120000'),
('004', '202310004', '赵六', '2', '20241220120000'),
('005', '202310005', '钱七', '1', '20241220120000');

-- 2. 创建测试学籍信息
INSERT INTO T_GXXS_BKSXJJBXX (WYBS, XH, XDYXBM, ZYBM, ZYBBM, NJ, TTIMESTAMP) VALUES
('001', '202310001', 'FD000020', '00001', 'CS2023001', '2023级', '20241220120000'),
('002', '202310002', 'FD000020', '00001', 'CS2023001', '2023级', '20241220120000'),
('003', '202310003', 'FD000021', '00002', 'EE2023001', '2023级', '20241220120000'),
('004', '202310004', 'FD000021', '00002', 'EE2023001', '2023级', '20241220120000'),
('005', '202310005', 'FD000020', '00001', 'CS2023002', '2023级', '20241220120000');

-- 3. 创建组织机构测试数据
INSERT INTO DM_XB_ZZJGM (ZZJGM, ZZJGMC, SFQY, SJCS) VALUES
('FD000020', '计算机科学与技术学院', 1, '20241220120000'),
('FD000021', '电子工程与信息科学系', 1, '20241220120000'),
('FD000022', '数学科学学院', 1, '20241220120000');

-- 4. 创建专业测试数据
INSERT INTO DM_XB_BKSZYM (ZYM, ZYMC, SFQY, SJCS) VALUES
('00001', '计算机科学与技术', 1, '20241220120000'),
('00002', '电子信息工程', 1, '20241220120000'),
('00003', '数学与应用数学', 1, '20241220120000');

-- 5. 创建专业班级测试数据
INSERT INTO DM_XB_BKSZYBJM (ZYBBM, ZYBBMC, NJ, SFQY, SJCS) VALUES
('CS2023001', '计算机2023-1班', '2023', 1, '20241220120000'),
('CS2023002', '计算机2023-2班', '2023', 1, '20241220120000'),
('EE2023001', '电子2023-1班', '2023', 1, '20241220120000');

-- 6. 创建预警信息测试数据
INSERT INTO T_GXYS_YJXX (WYBS, XH, YJLX_M, YJDJ_M, YJZT_M, YJMS, CJSJ, TTIMESTAMP) VALUES
-- 张三：学业预警(高风险) + 轨迹预警(中风险)
('W001', '202310001', '01', '3', '01', '学业成绩严重下滑，多门课程不及格', '20241220120000', '20241220120000'),
('W002', '202310001', '11', '2', '01', '经常缺课，行为轨迹异常', '20241220120000', '20241220120000'),

-- 李四：生活预警(低风险)
('W003', '202310002', '21', '1', '01', '生活作息不规律', '20241220120000', '20241220120000'),

-- 王五：心理预警(高风险)
('W004', '202310003', '31', '3', '01', '心理状态异常，需要重点关注', '20241220120000', '20241220120000'),

-- 赵六：学业预警(中风险) - 已解决
('W005', '202310004', '02', '2', '03', '期中考试成绩不理想，已通过辅导改善', '20241220120000', '20241220120000'),

-- 钱七：无预警记录（用于测试NONE状态）
('W006', '202310006', '01', '1', '04', '历史预警记录，已忽略', '20241220120000', '20241220120000');


-- ----------------------------
-- Table structure for T_GXTS_JYLSXX
-- ----------------------------
DROP TABLE IF EXISTS `T_GXTS_JYLSXX`;
CREATE TABLE `T_GXTS_JYLSXX` (
                                 `GID` VARCHAR(100) NOT NULL COMMENT 'GID',
                                 `XGH` VARCHAR(100) DEFAULT NULL COMMENT '学工号',
                                 `JYSJ` VARCHAR(20) DEFAULT NULL COMMENT '借阅时间',
                                 `GHSJ` VARCHAR(20) DEFAULT NULL COMMENT '归还时间',
                                 `TSGZJH` VARCHAR(100) DEFAULT NULL COMMENT '图书馆证件号',
                                 `TSFLH` VARCHAR(100) DEFAULT NULL COMMENT '图书分类号',
                                 `TSTM` VARCHAR(100) DEFAULT NULL COMMENT '图书题名',
                                 `TSTAMP` VARCHAR(20) DEFAULT NULL COMMENT '时间戳',
                                 `CQTS` VARCHAR(10) DEFAULT NULL COMMENT '超期天数',
                                 PRIMARY KEY (`GID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='借阅历史信息';

-- ----------------------------
-- Records for T_GXTS_JYLSXX
-- ----------------------------
INSERT INTO `T_GXTS_JYLSXX` (`GID`, `XGH`, `JYSJ`, `GHSJ`, `TSGZJH`, `TSFLH`, `TSTM`, `TSTAMP`, `CQTS`) VALUES
                                                                                                            ('d8f3b3e4-2b0a-4a6e-9f3b-8c8a1b9e2d3f', '202211001', '2024-03-15 10:22:00', '2024-04-14 18:00:00', 'L202211001', 'TP312', '深入理解计算机系统', '2024-04-14 18:05:11', '0'),
                                                                                                            ('a1e9c8b7-6d5f-4e3c-2b1a-098f7e6d5c4b', '202211001', '2024-04-01 11:30:00', '2024-05-10 09:15:00', 'L202211001', 'I247.5', '三体', '2024-05-10 09:20:01', '9'),
                                                                                                            ('f7e6d5c4-b3a2-1987-6543-21098fedcba9', '202309055', '2024-04-20 14:00:00', '2024-05-20 11:45:00', 'L202309055', 'O1-09', '数学之美', '2024-05-20 11:50:23', '0'),
                                                                                                            ('b2a19876-5432-fedc-ba98-76543210fedc', '202103210', '2024-05-02 16:45:00', NULL, 'L202103210', 'H319.4', '现代汉语', '2024-05-02 16:51:30', '0'),
                                                                                                            ('c3d2e1f0-a9b8-c7d6-e5f4-a3b2c1d0e9f8', '202309055', '2024-05-10 09:00:00', '2024-05-18 10:00:00', 'L202309055', 'G252', '图书馆学概论', '2024-05-18 10:05:00', '0'),
                                                                                                            ('e4f3g2h1-i0j9-k8l7-m6n5-o4p3q2r1s0t9', '202310001', '2024-09-15 14:30:00', '2024-10-15 16:00:00', 'L202310001', 'TP393', '人工智能导论', '2024-10-15 16:05:00', '0'),
                                                                                                            ('f5g4h3i2-j1k0-l9m8-n7o6-p5q4r3s2t1u0', '202310001', '2024-10-20 10:15:00', '2024-11-19 09:30:00', 'L202310001', 'TP312', 'Java编程思想', '2024-11-19 09:35:00', '0'),
                                                                                                            ('g6h5i4j3-k2l1-m0n9-o8p7-q6r5s4t3u2v1', '202310002', '2024-09-10 11:45:00', '2024-10-10 14:20:00', 'L202310002', 'O1-3', '高等数学', '2024-10-10 14:25:00', '0');

-- ----------------------------
-- Table structure for T_GXXS_BKSXQJDXX (本科生学期绩点信息表)
-- ----------------------------
DROP TABLE IF EXISTS `T_GXXS_BKSXQJDXX`;
CREATE TABLE `T_GXXS_BKSXQJDXX` (
    `WYBS` VARCHAR(100) NOT NULL COMMENT '唯一标识',
    `XH` VARCHAR(20) DEFAULT NULL COMMENT '学号',
    `XNXQ` VARCHAR(20) DEFAULT NULL COMMENT '学年学期',
    `GPA` DECIMAL(4,2) DEFAULT NULL COMMENT 'GPA',
    `JDFJF` DECIMAL(6,2) DEFAULT NULL COMMENT '加权平均分',
    `SSPJF` DECIMAL(6,2) DEFAULT NULL COMMENT '算术平均分',
    `ZXF` DECIMAL(6,1) DEFAULT NULL COMMENT '总学分',
    `TGZXF` DECIMAL(6,1) DEFAULT NULL COMMENT '通过总学分',
    `WTCZXF` DECIMAL(6,1) DEFAULT NULL COMMENT '未通过总学分',
    `SFCYPM` VARCHAR(2) DEFAULT NULL COMMENT '是否参与排名',
    `GPAJSFS` VARCHAR(10) DEFAULT NULL COMMENT 'GPA计算方式',
    `GXROSJ` VARCHAR(20) DEFAULT NULL COMMENT '更新时间',
    `TSTAMP` VARCHAR(20) DEFAULT NULL COMMENT '时间戳',
    PRIMARY KEY (`WYBS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='本科生学期绩点信息表';

-- ----------------------------
-- Records for T_GXXS_BKSXQJDXX
-- ----------------------------
INSERT INTO `T_GXXS_BKSXQJDXX` (`WYBS`, `XH`, `XNXQ`, `GPA`, `JDFJF`, `SSPJF`, `ZXF`, `TGZXF`, `WTCZXF`, `SFCYPM`, `GPAJSFS`, `GXROSJ`, `TSTAMP`) VALUES
('GPA001', '202310001', '2023-2024-1', 3.65, 85.2, 83.8, 25.0, 25.0, 0.0, '1', 'STANDARD', '20241220120000', '20241220120000'),
('GPA002', '202310001', '2023-2024-2', 3.72, 86.5, 84.2, 24.0, 24.0, 0.0, '1', 'STANDARD', '20241220120000', '20241220120000'),
('GPA003', '202310001', '2024-2025-1', 3.80, 87.8, 85.5, 26.0, 26.0, 0.0, '1', 'STANDARD', '20241220120000', '20241220120000'),
('GPA004', '202310002', '2023-2024-1', 3.45, 82.3, 80.1, 25.0, 23.0, 2.0, '1', 'STANDARD', '20241220120000', '20241220120000'),
('GPA005', '202310002', '2023-2024-2', 3.58, 84.1, 82.5, 24.0, 24.0, 0.0, '1', 'STANDARD', '20241220120000', '20241220120000'),
('GPA006', '202310003', '2023-2024-1', 3.90, 89.5, 87.2, 25.0, 25.0, 0.0, '1', 'STANDARD', '20241220120000', '20241220120000');

-- ----------------------------
-- Table structure for T_GXXS_BKSJXJXX (本科生奖学金信息表)
-- ----------------------------
DROP TABLE IF EXISTS `T_GXXS_BKSJXJXX`;
CREATE TABLE `T_GXXS_BKSJXJXX` (
    `WYBS` VARCHAR(100) NOT NULL COMMENT '唯一标识',
    `XH` VARCHAR(20) DEFAULT NULL COMMENT '学号',
    `HJXN` VARCHAR(20) DEFAULT NULL COMMENT '获奖学年',
    `JXJMC` VARCHAR(100) DEFAULT NULL COMMENT '奖学金名称',
    `JXJLX` VARCHAR(20) DEFAULT NULL COMMENT '奖学金类型',
    `JXJJE` DECIMAL(10,2) DEFAULT NULL COMMENT '奖学金金额',
    `HJRQ` VARCHAR(20) DEFAULT NULL COMMENT '获奖日期',
    `HJZT` VARCHAR(10) DEFAULT NULL COMMENT '获奖状态',
    `BZ` VARCHAR(500) DEFAULT NULL COMMENT '备注',
    `GXROSJ` VARCHAR(20) DEFAULT NULL COMMENT '更新时间',
    `TSTAMP` VARCHAR(20) DEFAULT NULL COMMENT '时间戳',
    PRIMARY KEY (`WYBS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='本科生奖学金信息表';

-- ----------------------------
-- Records for T_GXXS_BKSJXJXX
-- ----------------------------
INSERT INTO `T_GXXS_BKSJXJXX` (`WYBS`, `XH`, `HJXN`, `JXJMC`, `JXJLX`, `JXJJE`, `HJRQ`, `HJZT`, `BZ`, `GXROSJ`, `TSTAMP`) VALUES
('SCH001', '202310001', '2023-2024', '国家励志奖学金', 'JXJ001', 5000.00, '2024-10-15', '1', '学习成绩优秀', '20241220120000', '20241220120000'),
('SCH002', '202310001', '2023-2024', '校级三好学生奖学金', 'JXJ002', 2000.00, '2024-11-01', '1', '德智体美劳全面发展', '20241220120000', '20241220120000'),
('SCH003', '202310002', '2023-2024', '校级优秀学生奖学金', 'JXJ002', 1500.00, '2024-11-01', '1', '学习进步显著', '20241220120000', '20241220120000'),
('SCH004', '202310003', '2023-2024', '国家奖学金', 'JXJ001', 8000.00, '2024-10-01', '1', '学习成绩特别优秀', '20241220120000', '20241220120000');

-- ----------------------------
-- Table structure for T_GXTS_TSGZJTXXX (图书馆闸机通行信息表)
-- ----------------------------
DROP TABLE IF EXISTS `T_GXTS_TSGZJTXXX`;
CREATE TABLE `T_GXTS_TSGZJTXXX` (
    `WYBS` VARCHAR(100) NOT NULL COMMENT '唯一标识',
    `XGH` VARCHAR(20) DEFAULT NULL COMMENT '学工号',
    `XM` VARCHAR(50) DEFAULT NULL COMMENT '姓名',
    `RYLX` VARCHAR(10) DEFAULT NULL COMMENT '人员类型',
    `BMMC` VARCHAR(100) DEFAULT NULL COMMENT '部门名称',
    `TXSJ` VARCHAR(20) DEFAULT NULL COMMENT '通行时间',
    `TXLX` VARCHAR(10) DEFAULT NULL COMMENT '通行类型',
    `JCFX` VARCHAR(10) DEFAULT NULL COMMENT '进出方向',
    `TSGMC` VARCHAR(100) DEFAULT NULL COMMENT '图书馆名称',
    `TXMMC` VARCHAR(100) DEFAULT NULL COMMENT '通行门名称',
    `ZJBH` VARCHAR(50) DEFAULT NULL COMMENT '闸机编号',
    `TTIMESTAMP` VARCHAR(20) DEFAULT NULL COMMENT '时间戳',
    PRIMARY KEY (`WYBS`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图书馆闸机通行信息表';

-- ----------------------------
-- Records for T_GXTS_TSGZJTXXX
-- ----------------------------
INSERT INTO `T_GXTS_TSGZJTXXX` (`WYBS`, `XGH`, `XM`, `RYLX`, `BMMC`, `TXSJ`, `TXLX`, `JCFX`, `TSGMC`, `TXMMC`, `ZJBH`, `TTIMESTAMP`) VALUES
-- 张三的图书馆出入记录
('LIB001', '202310001', '张三', '01', '计算机科学与技术学院', '2024-09-15 08:30:00', '01', '01', '主图书馆', '东门', 'ZJ001', '20240915083000'),
('LIB002', '202310001', '张三', '01', '计算机科学与技术学院', '2024-09-15 12:00:00', '01', '02', '主图书馆', '东门', 'ZJ001', '20240915120000'),
('LIB003', '202310001', '张三', '01', '计算机科学与技术学院', '2024-09-16 14:20:00', '01', '01', '主图书馆', '东门', 'ZJ001', '20240916142000'),
('LIB004', '202310001', '张三', '01', '计算机科学与技术学院', '2024-09-16 18:30:00', '01', '02', '主图书馆', '东门', 'ZJ001', '20240916183000'),
('LIB005', '202310001', '张三', '01', '计算机科学与技术学院', '2024-10-10 09:15:00', '01', '01', '主图书馆', '东门', 'ZJ001', '20241010091500'),
('LIB006', '202310001', '张三', '01', '计算机科学与技术学院', '2024-10-10 17:45:00', '01', '02', '主图书馆', '东门', 'ZJ001', '20241010174500'),
-- 李四的图书馆出入记录
('LIB007', '202310002', '李四', '01', '计算机科学与技术学院', '2024-09-20 10:00:00', '01', '01', '主图书馆', '西门', 'ZJ002', '20240920100000'),
('LIB008', '202310002', '李四', '01', '计算机科学与技术学院', '2024-09-20 16:30:00', '01', '02', '主图书馆', '西门', 'ZJ002', '20240920163000'),
('LIB009', '202310002', '李四', '01', '计算机科学与技术学院', '2024-10-05 13:45:00', '01', '01', '主图书馆', '西门', 'ZJ002', '20241005134500'),
('LIB010', '202310002', '李四', '01', '计算机科学与技术学院', '2024-10-05 19:20:00', '01', '02', '主图书馆', '西门', 'ZJ002', '20241005192000');


-- ----------------------------
-- Table structure for TB_ACADEMIC_WARNING (学业预警信息表)
-- ----------------------------
DROP TABLE IF EXISTS `TB_ACADEMIC_WARNING`;
CREATE TABLE `TB_ACADEMIC_WARNING`  (
                                        `WYBS` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '唯一标识',
                                        `XH` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '预警学生学号(外键)',
                                        `WARNING_TYPE` VARCHAR(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '预警类型码(学业预警: 01)',
                                        `WARNING_STATUS` INT NULL DEFAULT NULL COMMENT '预警状态码(预警: 1, 解除: 0)',
                                        `SIMILARITY_STUDENT_XH` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '算法预测相似同学的学号',
                                        `EXAMPLE_STUDENT_XH` VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '算法预测榜样同学的学号',
                                        `EXAMPLE_DIFFERENCES` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '榜样差异描述',
                                        `GUIDANCE` VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '指导建议描述',
                                        PRIMARY KEY (`WYBS`) USING BTREE,
                                        INDEX `idx_xh`(`XH`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '学业预警信息表' ROW_FORMAT = Dynamic;


-- ----------------------------
-- Records of TB_ACADEMIC_WARNING (新版数据)
-- ----------------------------
-- 说明：
-- 1. 预警学生(XH)学号范围为 202310001-202310005。
-- 2. 相似/榜样学长学号均选用较早年份(2019级)，与预警学生学号拉开差距，符合“学长”的设定，确保能查询到推测的历史数据。
-- ----------------------------
INSERT INTO `TB_ACADEMIC_WARNING` (`WYBS`, `XH`, `WARNING_TYPE`, `WARNING_STATUS`, `SIMILARITY_STUDENT_XH`, `EXAMPLE_STUDENT_XH`, `EXAMPLE_DIFFERENCES`, `GUIDANCE`)
VALUES
    ('WARN_2023_001', '202310001', '01', 1, '201908015', '201901002', '榜样学长(202101002)大一平均绩点为3.8，您当前为2.1。该学长《高等数学》成绩为95分。', '您因《高等数学》课程不及格触发预警。建议您尽快联系授课老师，制定补习计划，并考虑参加学校的学业辅导中心。'),
    ('WARN_2023_002', '202310002', '01', 1, '201903021', '201905009', '榜样学长(202105009)在大一上学期修得24个学分，您当前获得学分仅为14个，低于最低要求。', '获得学分过少可能导致延期毕业。请立即与您的辅导员或学业导师会面，规划后续学期的选课和学习任务。'),
    ('WARN_2023_003', '202310003', '01', 1, '201911030', '201902005', '榜样学长(201902005)所有课程均一次性通过，您当前有两门公共选修课不及格。', '选修课同样计入总绩点。请评估个人兴趣与课程难度，合理选择重修课程。建议调整学习策略，平衡专业课与选修课的时间投入。'),
    ('WARN_2023_004', '202310004', '01', 1, '201907019', '201908003', '榜样学长(201908003)专业核心课平均分在90分以上，您当前专业核心课平均分在70分左右，呈下滑趋势。', '您的专业基础存在薄弱环节。建议您在课后投入更多时间复习和预习，并积极参加专业相关的学习小组，巩固知识体系。'),
    ('WARN_2023_005', '202310005', '01', 0, '201909012', '201904007', '该生曾因多门课程不及格被预警，与榜样学长(201904007)差距较大。', '通过暑期重修，该生所有课程成绩已达到及格线以上，绩点恢复至2.5以上。预警已于2024-03-10解除。请继续努力，保持进步。');