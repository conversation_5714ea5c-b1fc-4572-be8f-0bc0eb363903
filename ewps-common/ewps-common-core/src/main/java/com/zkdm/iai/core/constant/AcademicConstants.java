package com.zkdm.iai.core.constant;

/**
 * 学业相关常量定义
 * 
 * <AUTHOR>
 */
public interface AcademicConstants {
    
    /**
     * 毕业要求总学分
     */
    String GRADUATION_REQUIRED_CREDITS = "166";
    
    /**
     * 毕业要求最低GPA
     */
    String GRADUATION_REQUIRED_MIN_GPA = "4.3";
    
    /**
     * GPA满分值
     */
    String MAX_GPA = "4.0";
    
    /**
     * 学期格式示例
     */
    String SEMESTER_FORMAT_EXAMPLE = "2023-2024-1";
    
    /**
     * 奖学金类型码前缀（用于筛选奖学金类型的获奖记录）
     */
    String SCHOLARSHIP_TYPE_PREFIX = "JXJ";
}
