package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.entity.student.UndergraduateScholarship;
import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.AwardsVO;
import com.zkdm.iai.mapper.student.UndergraduateScholarshipMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.function.Consumer;

/**
 * 获奖情况信息提供者实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
class AwardsProviderImpl implements AcademicInfoProvider {

    private final UndergraduateScholarshipMapper scholarshipMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            long currentCount = 0;
            String comparison = "0";

            if (StringUtils.hasText(semester)) {
                // 指定学期的获奖查询（按学年筛选）
                String academicYear = SemesterDateUtil.extractAcademicYear(semester);
                if (StringUtils.hasText(academicYear)) {
                    currentCount = queryAwardsForAcademicYear(studentId, academicYear);
                    comparison = calculateAwardsComparison(studentId, semester);
                }
            } else {
                // 总获奖数量查询
                currentCount = queryTotalAwards(studentId);
            }

            var awardsVO = AwardsVO.builder()
                    .count(currentCount + "项")
                    .comparison(comparison)
                    .build();

            return builder -> builder.awardsInfo(awardsVO);
        } catch (Exception e) {
            log.error("获取获奖信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorAwardsVO();
        }
    }

    /**
     * 查询指定学年的获奖数量
     */
    private long queryAwardsForAcademicYear(String studentId, String academicYear) {
        var query = MpQueryUtils.<UndergraduateScholarship>lambdaQuery()
                .eq(UndergraduateScholarship::getStudentId, studentId)
                .eq(UndergraduateScholarship::getAwardAcademicYear, academicYear)
                .build();

        return scholarshipMapper.selectCount(query);
    }

    /**
     * 查询总获奖数量
     */
    private long queryTotalAwards(String studentId) {
        var query = MpQueryUtils.<UndergraduateScholarship>lambdaQuery()
                .eq(UndergraduateScholarship::getStudentId, studentId)
                .build();

        return scholarshipMapper.selectCount(query);
    }

    /**
     * 计算获奖对比
     */
    private String calculateAwardsComparison(String studentId, String semester) {
        String previousSemester = SemesterDateUtil.getPreviousSemester(semester);
        if (!StringUtils.hasText(previousSemester)) {
            return "0";
        }

        String currentAcademicYear = SemesterDateUtil.extractAcademicYear(semester);
        String previousAcademicYear = SemesterDateUtil.extractAcademicYear(previousSemester);

        if (!StringUtils.hasText(currentAcademicYear) || !StringUtils.hasText(previousAcademicYear)) {
            return "0";
        }

        long currentCount = queryAwardsForAcademicYear(studentId, currentAcademicYear);
        long previousCount = queryAwardsForAcademicYear(studentId, previousAcademicYear);

        long diff = currentCount - previousCount;
        return diff >= 0 ? "+" + diff : String.valueOf(diff);
    }

    /**
     * 构建错误时的获奖VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorAwardsVO() {
        var awardsVO = AwardsVO.builder()
                .count("0项")
                .comparison("0")
                .build();
        return builder -> builder.awardsInfo(awardsVO);
    }
}