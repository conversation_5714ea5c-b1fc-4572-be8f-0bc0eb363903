package com.zkdm.iai.provider.impl.academicinfo.strategy.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.strategy.AbstractGpaDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 相似学生GPA数据获取策略
 * <p>
 * 专门处理相似学生的GPA数据获取逻辑，用于为预警学生提供现实可达的参考轨迹。
 * <p>
 * 业务规则：
 * - 获取相似学生当前学期之后的GPA记录作为参考轨迹
 * - 用于展示与预警学生学业水平相近的学长发展路径
 * - 数据范围：当前学期之后的所有学期
 * - 为预警学生提供现实可达的改进目标
 * <p>
 * 数据特点：
 * - 相似性：基于算法匹配的相似学业水平学生
 * - 现实性：提供更贴近预警学生实际情况的参考
 * - 可达性：相比榜样学生，目标更容易实现
 * - 渐进性：展示逐步改善的可能路径
 * <p>
 * 应用场景：
 * - 现实目标设定：帮助预警学生设定可达成的短期目标
 * - 信心建立：通过相似案例增强学生改进信心
 * - 路径参考：提供具体的学业改善策略参考
 * - 同伴学习：利用同伴效应促进学习动机
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class SimilarStudentGpaStrategy extends AbstractGpaDataStrategy {
    
    /**
     * 构造函数
     * 
     * @param semesterGpaMapper 学期绩点数据访问接口
     */
    public SimilarStudentGpaStrategy(UndergraduateSemesterGpaMapper semesterGpaMapper) {
        super(semesterGpaMapper);
    }
    
    /**
     * 获取学生类型
     * 
     * @return 相似学生类型
     */
    @Override
    public StudentType getStudentType() {
        return StudentType.SIMILAR;
    }
    
    /**
     * 过滤相似学生的GPA数据
     * <p>
     * 相似学生的数据过滤规则：
     * 1. 只返回当前学期之后的数据，作为预警学生的参考轨迹
     * 2. 如果没有指定当前学期，返回所有数据
     * 3. 保持数据的真实性，不过度美化
     * 4. 按学期时间顺序排列
     * 5. 重点关注改进趋势而非绝对数值
     * 
     * @param rawGpaList 原始GPA数据列表
     * @param currentSemester 当前学期，可以为空
     * @return 过滤后的GPA数据列表
     */
    @Override
    protected List<UndergraduateSemesterGpa> filterGpaData(
            List<UndergraduateSemesterGpa> rawGpaList, 
            String currentSemester) {
        
        log.debug("开始过滤相似学生GPA数据，原始数据量：{}, 当前学期：{}", 
                rawGpaList.size(), currentSemester);
        
        // 如果没有指定当前学期，返回所有有效数据
        if (!StringUtils.hasText(currentSemester)) {
            log.debug("当前学期为空，返回相似学生所有GPA数据");
            return filterValidRecords(rawGpaList);
        }
        
        // 过滤当前学期之后的数据
        List<UndergraduateSemesterGpa> filteredList = rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) > 0)
                .collect(Collectors.toList());
        
        log.debug("相似学生GPA数据过滤完成，过滤后数据量：{}", filteredList.size());
        
        // 分析相似学生的改进轨迹
        if (!filteredList.isEmpty()) {
            analyzeImprovementTrajectory(filteredList);
        }
        
        return filteredList;
    }
    
    /**
     * 过滤有效记录
     * <p>
     * 当没有指定当前学期时，仍需要过滤掉无效记录
     * 
     * @param rawGpaList 原始GPA数据列表
     * @return 有效记录列表
     */
    private List<UndergraduateSemesterGpa> filterValidRecords(List<UndergraduateSemesterGpa> rawGpaList) {
        return rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .collect(Collectors.toList());
    }
    
    /**
     * 分析相似学生的改进轨迹
     * <p>
     * 分析相似学生的GPA变化趋势，为预警学生提供参考信息
     * 
     * @param gpaList 相似学生的GPA数据列表
     */
    private void analyzeImprovementTrajectory(List<UndergraduateSemesterGpa> gpaList) {
        if (gpaList.size() < 2) {
            log.debug("相似学生数据不足，无法分析改进轨迹");
            return;
        }
        
        // 计算GPA变化趋势
        double firstGpa = gpaList.get(0).getGpa() != null ? gpaList.get(0).getGpa().doubleValue() : 0.0;
        double lastGpa = gpaList.get(gpaList.size() - 1).getGpa() != null ? 
                gpaList.get(gpaList.size() - 1).getGpa().doubleValue() : 0.0;
        
        double improvement = lastGpa - firstGpa;
        String trend = improvement > 0 ? "上升" : (improvement < 0 ? "下降" : "稳定");
        
        log.debug("相似学生改进轨迹分析：起始GPA={:.2f}, 结束GPA={:.2f}, 变化={:.2f}, 趋势={}", 
                firstGpa, lastGpa, improvement, trend);
        
        // 计算平均GPA
        double avgGpa = gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                .average()
                .orElse(0.0);
        
        log.debug("相似学生参考轨迹平均GPA：{:.2f}", avgGpa);
    }
    
    /**
     * 验证GPA记录是否有效
     * <p>
     * 对于相似学生，我们需要保持数据的真实性：
     * - 学期信息必须完整
     * - 接受各种GPA水平（包括较低的GPA）
     * - 学期格式必须正确
     * - 重点关注数据的完整性而非质量
     * 
     * @param gpa GPA记录
     * @return true表示有效，false表示无效
     */
    private boolean isValidGpaRecord(UndergraduateSemesterGpa gpa) {
        if (gpa == null) {
            return false;
        }
        
        // 检查学期信息
        if (!StringUtils.hasText(gpa.getAcademicYearSemester())) {
            log.trace("相似学生GPA记录学期信息为空，跳过：{}", gpa.getId());
            return false;
        }
        
        // 检查学期格式
        if (!isValidSemesterFormat(gpa.getAcademicYearSemester())) {
            log.trace("相似学生GPA记录学期格式无效，跳过：{}", gpa.getAcademicYearSemester());
            return false;
        }
        
        // 对于相似学生，接受所有合理的GPA值（包括0和较低的值）
        // 这体现了相似学生的真实性和可达性
        return true;
    }
    
    /**
     * 验证学期格式
     * 
     * @param semester 学期字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidSemesterFormat(String semester) {
        // 学期格式：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }
    
    /**
     * 获取策略的详细描述
     * 
     * @return 策略描述
     */
    @Override
    public String getStrategyDescription() {
        return "相似学生GPA数据获取策略：获取学业水平相近的学长当前学期之后的GPA轨迹，为预警学生提供现实可达的参考目标";
    }
    
    /**
     * 检查是否支持指定的学生类型
     * 
     * @param studentType 学生类型
     * @return true表示支持相似学生类型
     */
    @Override
    public boolean supports(StudentType studentType) {
        return StudentType.SIMILAR == studentType;
    }
    
    /**
     * 重写参数验证，添加相似学生特有的验证逻辑
     * 
     * @param studentId 学生学号
     * @param referenceStudentId 参考学生学号
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    protected void validateParameters(String studentId, String referenceStudentId) {
        super.validateParameters(studentId, referenceStudentId);
        
        // 可以添加相似学生特有的验证逻辑
        // 例如：检查学生是否确实与预警学生相似
        if (!hasGpaData(studentId)) {
            log.warn("相似学生{}没有GPA数据，可能影响参考轨迹的有效性", studentId);
        }
    }
}
