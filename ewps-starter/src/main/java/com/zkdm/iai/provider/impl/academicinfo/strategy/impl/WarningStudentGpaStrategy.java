package com.zkdm.iai.provider.impl.academicinfo.strategy.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.strategy.AbstractGpaDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 预警学生GPA数据获取策略
 * <p>
 * 专门处理预警学生的GPA数据获取逻辑，继承自抽象基类以复用通用功能。
 * <p>
 * 业务规则：
 * - 获取预警学生从入学到当前学期的所有GPA记录
 * - 用于展示学生的历史学业表现轨迹
 * - 数据范围：当前学期及之前的所有学期
 * - 按学期时间顺序排列，便于前端绘制趋势图
 * <p>
 * 数据特点：
 * - 完整性：包含学生的完整学业历程
 * - 真实性：基于实际的学业成绩数据
 * - 连续性：按时间顺序展示GPA变化趋势
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class WarningStudentGpaStrategy extends AbstractGpaDataStrategy {
    
    /**
     * 构造函数
     * 
     * @param semesterGpaMapper 学期绩点数据访问接口
     */
    public WarningStudentGpaStrategy(UndergraduateSemesterGpaMapper semesterGpaMapper) {
        super(semesterGpaMapper);
    }
    
    /**
     * 获取学生类型
     * 
     * @return 预警学生类型
     */
    @Override
    public StudentType getStudentType() {
        return StudentType.WARNING;
    }
    
    /**
     * 过滤预警学生的GPA数据
     * <p>
     * 预警学生的数据过滤规则：
     * 1. 如果指定了当前学期，只返回当前学期及之前的数据
     * 2. 如果没有指定当前学期，返回所有历史数据
     * 3. 按学期时间顺序排列
     * 4. 确保数据的完整性和连续性
     * 
     * @param rawGpaList 原始GPA数据列表
     * @param currentSemester 当前学期，可以为空
     * @return 过滤后的GPA数据列表
     */
    @Override
    protected List<UndergraduateSemesterGpa> filterGpaData(
            List<UndergraduateSemesterGpa> rawGpaList, 
            String currentSemester) {
        
        log.debug("开始过滤预警学生GPA数据，原始数据量：{}, 当前学期：{}", 
                rawGpaList.size(), currentSemester);
        
        // 如果没有指定当前学期，返回所有数据
        if (!StringUtils.hasText(currentSemester)) {
            log.debug("当前学期为空，返回所有历史GPA数据");
            return rawGpaList;
        }
        
        // 过滤当前学期及之前的数据
        List<UndergraduateSemesterGpa> filteredList = rawGpaList.stream()
                .filter(gpa -> isValidGpaRecord(gpa))
                .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) <= 0)
                .collect(Collectors.toList());
        
        log.debug("预警学生GPA数据过滤完成，过滤后数据量：{}", filteredList.size());
        
        // 记录数据范围信息
        if (!filteredList.isEmpty()) {
            String earliestSemester = filteredList.get(0).getAcademicYearSemester();
            String latestSemester = filteredList.get(filteredList.size() - 1).getAcademicYearSemester();
            log.debug("预警学生GPA数据范围：{} 至 {}", earliestSemester, latestSemester);
        }
        
        return filteredList;
    }
    
    /**
     * 验证GPA记录是否有效
     * <p>
     * 对于预警学生，我们需要更严格的数据验证：
     * - 学期信息必须完整
     * - GPA值可以为0（表示该学期表现不佳）
     * - 学期格式必须正确
     * 
     * @param gpa GPA记录
     * @return true表示有效，false表示无效
     */
    private boolean isValidGpaRecord(UndergraduateSemesterGpa gpa) {
        if (gpa == null) {
            return false;
        }
        
        // 检查学期信息
        if (!StringUtils.hasText(gpa.getAcademicYearSemester())) {
            log.trace("GPA记录学期信息为空，跳过：{}", gpa.getId());
            return false;
        }
        
        // 检查学期格式
        if (!isValidSemesterFormat(gpa.getAcademicYearSemester())) {
            log.trace("GPA记录学期格式无效，跳过：{}", gpa.getAcademicYearSemester());
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证学期格式
     * 
     * @param semester 学期字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidSemesterFormat(String semester) {
        // 学期格式：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }
    
    /**
     * 获取策略的详细描述
     * 
     * @return 策略描述
     */
    @Override
    public String getStrategyDescription() {
        return "预警学生GPA数据获取策略：获取学生从入学到当前学期的完整GPA历史记录，用于分析学业发展趋势";
    }
    
    /**
     * 检查是否支持指定的学生类型
     * 
     * @param studentType 学生类型
     * @return true表示支持预警学生类型
     */
    @Override
    public boolean supports(StudentType studentType) {
        return StudentType.WARNING == studentType;
    }
}
