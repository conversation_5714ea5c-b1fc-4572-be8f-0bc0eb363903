package com.zkdm.iai.provider.impl.academicinfo.strategy.impl;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.strategy.AbstractGpaDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 榜样学生GPA数据获取策略
 * <p>
 * 专门处理榜样学生的GPA数据获取逻辑，用于为预警学生提供学习目标和激励。
 * <p>
 * 业务规则：
 * - 获取榜样学生当前学期之后的GPA记录作为目标轨迹
 * - 用于展示优秀学长的学业发展路径
 * - 数据范围：当前学期之后的所有学期
 * - 为预警学生提供可追求的学业目标
 * <p>
 * 数据特点：
 * - 目标性：展示优秀学生的未来发展轨迹
 * - 激励性：为预警学生提供正面的学习榜样
 * - 可达性：基于真实的优秀学生数据，具有可实现性
 * <p>
 * 应用场景：
 * - 学业规划：帮助预警学生制定学习目标
 * - 动机激发：通过榜样的力量激励学生努力
 * - 路径指导：提供具体的学业提升路径参考
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
public class ExemplaryStudentGpaStrategy extends AbstractGpaDataStrategy {
    
    /**
     * 构造函数
     * 
     * @param semesterGpaMapper 学期绩点数据访问接口
     */
    public ExemplaryStudentGpaStrategy(UndergraduateSemesterGpaMapper semesterGpaMapper) {
        super(semesterGpaMapper);
    }
    
    /**
     * 获取学生类型
     * 
     * @return 榜样学生类型
     */
    @Override
    public StudentType getStudentType() {
        return StudentType.EXEMPLARY;
    }
    
    /**
     * 过滤榜样学生的GPA数据
     * <p>
     * 榜样学生的数据过滤规则：
     * 1. 只返回当前学期之后的数据，作为预警学生的目标轨迹
     * 2. 如果没有指定当前学期，返回所有数据
     * 3. 确保数据质量，过滤掉异常记录
     * 4. 按学期时间顺序排列
     * 
     * @param rawGpaList 原始GPA数据列表
     * @param currentSemester 当前学期，可以为空
     * @return 过滤后的GPA数据列表
     */
    @Override
    protected List<UndergraduateSemesterGpa> filterGpaData(
            List<UndergraduateSemesterGpa> rawGpaList, 
            String currentSemester) {
        
        log.debug("开始过滤榜样学生GPA数据，原始数据量：{}, 当前学期：{}", 
                rawGpaList.size(), currentSemester);
        
        // 如果没有指定当前学期，返回所有数据
        if (!StringUtils.hasText(currentSemester)) {
            log.debug("当前学期为空，返回榜样学生所有GPA数据");
            return filterValidRecords(rawGpaList);
        }
        
        // 过滤当前学期之后的数据
        List<UndergraduateSemesterGpa> filteredList = rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) > 0)
                .collect(Collectors.toList());
        
        log.debug("榜样学生GPA数据过滤完成，过滤后数据量：{}", filteredList.size());
        
        // 记录目标轨迹信息
        if (!filteredList.isEmpty()) {
            String firstSemester = filteredList.get(0).getAcademicYearSemester();
            String lastSemester = filteredList.get(filteredList.size() - 1).getAcademicYearSemester();
            log.debug("榜样学生目标轨迹范围：{} 至 {}", firstSemester, lastSemester);
            
            // 计算平均GPA作为目标参考
            double avgGpa = filteredList.stream()
                    .filter(gpa -> gpa.getGpa() != null)
                    .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                    .average()
                    .orElse(0.0);
            log.debug("榜样学生目标轨迹平均GPA：{:.2f}", avgGpa);
        }
        
        return filteredList;
    }
    
    /**
     * 过滤有效记录
     * <p>
     * 当没有指定当前学期时，仍需要过滤掉无效记录
     * 
     * @param rawGpaList 原始GPA数据列表
     * @return 有效记录列表
     */
    private List<UndergraduateSemesterGpa> filterValidRecords(List<UndergraduateSemesterGpa> rawGpaList) {
        return rawGpaList.stream()
                .filter(this::isValidGpaRecord)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证GPA记录是否有效
     * <p>
     * 对于榜样学生，我们需要确保数据质量：
     * - 学期信息必须完整
     * - GPA值应该相对较高（体现榜样特质）
     * - 学期格式必须正确
     * 
     * @param gpa GPA记录
     * @return true表示有效，false表示无效
     */
    private boolean isValidGpaRecord(UndergraduateSemesterGpa gpa) {
        if (gpa == null) {
            return false;
        }
        
        // 检查学期信息
        if (!StringUtils.hasText(gpa.getAcademicYearSemester())) {
            log.trace("榜样学生GPA记录学期信息为空，跳过：{}", gpa.getId());
            return false;
        }
        
        // 检查学期格式
        if (!isValidSemesterFormat(gpa.getAcademicYearSemester())) {
            log.trace("榜样学生GPA记录学期格式无效，跳过：{}", gpa.getAcademicYearSemester());
            return false;
        }
        
        // 对于榜样学生，可以添加GPA质量检查
        if (gpa.getGpa() != null && gpa.getGpa().doubleValue() < 0) {
            log.trace("榜样学生GPA值异常，跳过：{}", gpa.getGpa());
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证学期格式
     * 
     * @param semester 学期字符串
     * @return true表示格式正确，false表示格式错误
     */
    private boolean isValidSemesterFormat(String semester) {
        // 学期格式：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }
    
    /**
     * 获取策略的详细描述
     * 
     * @return 策略描述
     */
    @Override
    public String getStrategyDescription() {
        return "榜样学生GPA数据获取策略：获取优秀学长当前学期之后的GPA轨迹，为预警学生提供学习目标和激励";
    }
    
    /**
     * 检查是否支持指定的学生类型
     * 
     * @param studentType 学生类型
     * @return true表示支持榜样学生类型
     */
    @Override
    public boolean supports(StudentType studentType) {
        return StudentType.EXEMPLARY == studentType;
    }
    
    /**
     * 重写参数验证，添加榜样学生特有的验证逻辑
     * 
     * @param studentId 学生学号
     * @param referenceStudentId 参考学生学号
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    @Override
    protected void validateParameters(String studentId, String referenceStudentId) {
        super.validateParameters(studentId, referenceStudentId);
        
        // 可以添加榜样学生特有的验证逻辑
        // 例如：检查学生是否确实是榜样学生
        if (!hasGpaData(studentId)) {
            log.warn("榜样学生{}没有GPA数据，可能影响目标轨迹的准确性", studentId);
        }
    }
}
