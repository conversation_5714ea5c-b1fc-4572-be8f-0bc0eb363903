package com.zkdm.iai.provider.impl.academicinfo.strategy;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.utils.GpaDataConverter;
import com.zkdm.iai.provider.impl.academicinfo.utils.SemesterCalculator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * GPA数据策略抽象基类
 * <p>
 * 使用模板方法模式定义GPA数据获取的通用流程，具体的数据过滤逻辑由子类实现。
 * 提供了通用的数据查询、验证、转换等功能，减少代码重复。
 * <p>
 * 模板方法模式的优势：
 * - 代码复用：公共逻辑在基类中实现
 * - 扩展性：子类只需实现特定的业务逻辑
 * - 一致性：确保所有策略遵循相同的处理流程
 * - 维护性：公共逻辑的修改只需在基类中进行
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public abstract class AbstractGpaDataStrategy implements GpaDataStrategy {
    
    /**
     * 学期绩点数据访问接口
     */
    protected final UndergraduateSemesterGpaMapper semesterGpaMapper;
    
    /**
     * 模板方法：获取GPA数据的标准流程
     * <p>
     * 定义了获取GPA数据的标准流程：
     * 1. 参数验证
     * 2. 查询原始数据
     * 3. 数据过滤（由子类实现）
     * 4. 数据转换
     * 5. 异常处理
     * 
     * @param studentId 学生学号
     * @param currentSemester 当前学期
     * @param referenceStudentId 参考学生学号
     * @return GPA数据列表
     */
    @Override
    public final List<Map<String, String>> getGpaData(
            String studentId, 
            String currentSemester, 
            String referenceStudentId) {
        
        try {
            // 1. 参数验证
            validateParameters(studentId, referenceStudentId);
            
            log.debug("开始获取{}的GPA数据，学生：{}, 当前学期：{}", 
                    getStudentType().getTypeName(), studentId, currentSemester);
            
            // 2. 查询原始数据
            List<UndergraduateSemesterGpa> rawGpaList = queryRawGpaData(studentId);
            
            if (rawGpaList.isEmpty()) {
                log.debug("学生{}没有GPA数据", studentId);
                return GpaDataConverter.buildEmptyGpaData();
            }
            
            // 3. 数据过滤（由子类实现具体逻辑）
            List<UndergraduateSemesterGpa> filteredGpaList = filterGpaData(rawGpaList, currentSemester);
            
            // 4. 数据转换
            List<Map<String, String>> result = GpaDataConverter.convertGpaListToMapList(
                    filteredGpaList, referenceStudentId);
            
            log.debug("{}的GPA数据获取完成，原始数据{}条，过滤后{}条", 
                    getStudentType().getTypeName(), rawGpaList.size(), result.size());
            
            return result;
            
        } catch (IllegalArgumentException e) {
            log.warn("获取{}的GPA数据失败，参数错误：{}", getStudentType().getTypeName(), e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取{}的GPA数据失败，学生：{}", getStudentType().getTypeName(), studentId, e);
            return handleException(e);
        }
    }
    
    /**
     * 抽象方法：过滤GPA数据
     * <p>
     * 由子类实现具体的数据过滤逻辑：
     * - 预警学生策略：过滤当前学期及之前的数据
     * - 榜样学生策略：过滤当前学期之后的数据
     * - 相似学生策略：过滤当前学期之后的数据
     * 
     * @param rawGpaList 原始GPA数据列表
     * @param currentSemester 当前学期
     * @return 过滤后的GPA数据列表
     */
    protected abstract List<UndergraduateSemesterGpa> filterGpaData(
            List<UndergraduateSemesterGpa> rawGpaList, 
            String currentSemester);
    
    /**
     * 验证输入参数
     * 
     * @param studentId 学生学号
     * @param referenceStudentId 参考学生学号
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    protected void validateParameters(String studentId, String referenceStudentId) {
        if (!StringUtils.hasText(studentId)) {
            throw new IllegalArgumentException("学生学号不能为空");
        }
        
        if (!StringUtils.hasText(referenceStudentId)) {
            throw new IllegalArgumentException("参考学生学号不能为空");
        }
    }
    
    /**
     * 查询原始GPA数据
     * <p>
     * 从数据库查询指定学生的所有GPA记录，按学期排序
     * 
     * @param studentId 学生学号
     * @return 原始GPA数据列表
     */
    protected List<UndergraduateSemesterGpa> queryRawGpaData(String studentId) {
        log.trace("查询学生{}的原始GPA数据", studentId);
        
        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .orderByAsc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();
        
        List<UndergraduateSemesterGpa> gpaList = semesterGpaMapper.selectList(query);
        
        // 数据清洗：移除无效记录并去重
        return GpaDataConverter.cleanGpaData(gpaList);
    }
    
    /**
     * 使用学期计算器进行数据过滤
     * <p>
     * 提供给子类使用的便捷方法，基于学生类型自动选择过滤策略
     * 
     * @param gpaList 原始GPA数据列表
     * @param currentSemester 当前学期
     * @return 过滤后的GPA数据列表
     */
    protected List<UndergraduateSemesterGpa> filterBySemesterCalculator(
            List<UndergraduateSemesterGpa> gpaList, 
            String currentSemester) {
        
        return SemesterCalculator.filterGpaDataBySemester(gpaList, currentSemester, getStudentType());
    }
    
    /**
     * 处理异常情况
     * <p>
     * 当发生异常时，返回空数据以确保接口稳定性
     * 
     * @param e 异常对象
     * @return 空的GPA数据列表
     */
    protected List<Map<String, String>> handleException(Exception e) {
        log.warn("处理异常，返回空数据：{}", e.getMessage());
        return GpaDataConverter.buildEmptyGpaData();
    }
    
    /**
     * 检查学生是否存在GPA数据
     * 
     * @param studentId 学生学号
     * @return true表示存在数据，false表示不存在
     */
    protected boolean hasGpaData(String studentId) {
        if (!StringUtils.hasText(studentId)) {
            return false;
        }
        
        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .last("LIMIT 1")
                .build();
        
        return semesterGpaMapper.selectCount(query) > 0;
    }
    
    /**
     * 记录策略执行的详细信息
     * 
     * @param studentId 学生学号
     * @param currentSemester 当前学期
     * @param dataCount 数据条数
     */
    protected void logStrategyExecution(String studentId, String currentSemester, int dataCount) {
        log.info("策略执行完成 - 类型：{}, 学生：{}, 学期：{}, 数据量：{}", 
                getStudentType().getTypeName(), studentId, currentSemester, dataCount);
    }
}
