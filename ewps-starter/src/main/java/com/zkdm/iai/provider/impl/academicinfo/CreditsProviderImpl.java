package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.core.constant.AcademicConstants;
import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.CreditsVO;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.Consumer;

/**
 * 学分信息提供者实现类
 * <p>
 * 负责提供学生的学分相关信息，包括：
 * - 当前已获得学分统计
 * - 毕业要求总学分对比
 * - 与上学期学分差异计算
 * <p>
 * 数据来源：T_GXXS_BKSXQJDXX（本科生学期绩点信息表）
 * 业务规则：
 * - 支持按学期筛选学分信息
 * - 自动计算与上学期的学分差异
 * - 使用常量定义毕业要求学分（166学分）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
class CreditsProviderImpl implements AcademicInfoProvider {

    private final UndergraduateSemesterGpaMapper semesterGpaMapper;

    /**
     * 提供学分信息的构建行为（不指定学期）
     * <p>
     * 当不指定学期时，返回学生的总学分信息
     *
     * @param studentId 学生学号，不能为空
     * @return Consumer函数，用于构建学分信息VO
     */
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    /**
     * 提供学分信息的构建行为（支持学期筛选）
     * <p>
     * 核心业务逻辑：
     * 1. 根据是否指定学期，选择不同的查询策略
     * 2. 指定学期时：查询该学期的学分，并计算与上学期的差异
     * 3. 未指定学期时：查询总学分
     * 4. 格式化学分显示，构建VO对象
     *
     * @param studentId 学生学号，不能为空
     * @param semester 学期参数，可选，格式如：2023-2024-1
     * @return Consumer函数，用于构建学分信息VO
     */
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            log.debug("开始获取学生{}的学分信息，学期：{}", studentId, semester);

            BigDecimal currentCredits;
            String comparison = "0";

            if (StringUtils.hasText(semester)) {
                // 指定学期的学分查询：查询该学期的通过学分
                log.debug("查询指定学期{}的学分信息", semester);
                currentCredits = queryCreditsForSemester(studentId, semester);
                comparison = calculateCreditsComparison(studentId, semester);
            } else {
                // 总学分查询：累计所有学期的通过学分
                log.debug("查询总学分信息");
                currentCredits = queryTotalCredits(studentId);
            }

            String currentCreditsStr = formatCredits(currentCredits);
            log.debug("学生{}的学分信息：当前学分={}, 对比值={}", studentId, currentCreditsStr, comparison);

            var creditsVO = CreditsVO.builder()
                    .currentCredits(currentCreditsStr)
                    .totalCredits(AcademicConstants.GRADUATION_REQUIRED_CREDITS)
                    .comparison(comparison)
                    .build();

            return builder -> builder.creditsInfo(creditsVO);
        } catch (Exception e) {
            log.error("获取学分信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorCreditsVO();
        }
    }

    /**
     * 查询指定学期的学分
     * <p>
     * 通过学生ID和学期查询该学期的通过学分数
     *
     * @param studentId 学生学号
     * @param semester 学期，格式如：2023-2024-1
     * @return 该学期的通过学分，查询失败返回0
     */
    private BigDecimal queryCreditsForSemester(String studentId, String semester) {
        log.debug("查询学生{}在学期{}的学分", studentId, semester);

        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .eq(UndergraduateSemesterGpa::getAcademicYearSemester, semester)
                .build();

        UndergraduateSemesterGpa gpaInfo = semesterGpaMapper.selectOne(query);
        BigDecimal credits = gpaInfo != null && gpaInfo.getPassedCredits() != null ?
                gpaInfo.getPassedCredits() : BigDecimal.ZERO;

        log.debug("学生{}在学期{}的学分：{}", studentId, semester, credits);
        return credits;
    }

    /**
     * 查询总学分
     * <p>
     * 查询学生所有学期的累计通过学分
     *
     * @param studentId 学生学号
     * @return 累计通过学分，查询失败返回null
     */
    private BigDecimal queryTotalCredits(String studentId) {
        log.debug("查询学生{}的总学分", studentId);

        BigDecimal totalCredits = semesterGpaMapper.selectTotalPassedCredits(studentId);
        log.debug("学生{}的总学分：{}", studentId, totalCredits);

        return totalCredits;
    }

    /**
     * 计算学分对比
     * <p>
     * 计算当前学期与上一学期的学分差异
     * 业务逻辑：
     * 1. 根据当前学期计算上一学期
     * 2. 分别查询两个学期的学分
     * 3. 计算差异并格式化为带符号的字符串
     *
     * @param studentId 学生学号
     * @param semester 当前学期
     * @return 学分差异，格式如："+2.5"、"-1.0"、"0"
     */
    private String calculateCreditsComparison(String studentId, String semester) {
        log.debug("计算学生{}在学期{}的学分对比", studentId, semester);

        String previousSemester = SemesterDateUtil.getPreviousSemester(semester);
        if (!StringUtils.hasText(previousSemester)) {
            log.debug("无法计算上一学期，返回默认值0");
            return "0";
        }

        BigDecimal currentCredits = queryCreditsForSemester(studentId, semester);
        BigDecimal previousCredits = queryCreditsForSemester(studentId, previousSemester);

        if (currentCredits != null && previousCredits != null) {
            BigDecimal diff = currentCredits.subtract(previousCredits);
            String result = diff.compareTo(BigDecimal.ZERO) >= 0 ?
                    "+" + diff.setScale(1, RoundingMode.HALF_UP) :
                    diff.setScale(1, RoundingMode.HALF_UP).toString();

            log.debug("学分对比结果：当前学期={}, 上学期={}, 差异={}",
                    currentCredits, previousCredits, result);
            return result;
        }

        log.debug("学分数据不完整，返回默认值0");
        return "0";
    }

    /**
     * 格式化学分显示
     * <p>
     * 将BigDecimal类型的学分格式化为字符串，保留1位小数
     *
     * @param credits 学分数值
     * @return 格式化后的学分字符串，如："25.5"
     */
    private String formatCredits(BigDecimal credits) {
        return credits != null ?
                credits.setScale(1, RoundingMode.HALF_UP).toString() : "0.0";
    }

    /**
     * 构建错误时的学分VO
     * <p>
     * 当查询学分信息发生异常时，返回默认的学分信息
     * 确保接口的稳定性和用户体验
     *
     * @return Consumer函数，用于构建默认的学分信息VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorCreditsVO() {
        log.warn("构建错误时的学分VO");

        var creditsVO = CreditsVO.builder()
                .currentCredits("0.0")
                .totalCredits(AcademicConstants.GRADUATION_REQUIRED_CREDITS)
                .comparison("0")
                .build();
        return builder -> builder.creditsInfo(creditsVO);
    }
}
