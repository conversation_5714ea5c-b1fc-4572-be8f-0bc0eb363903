package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.entity.library.BorrowingHistoryInfo;
import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.BooksBorrowedVO;
import com.zkdm.iai.mapper.library.BorrowingHistoryInfoMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.function.Consumer;

/**
 * 图书借阅数量信息提供者实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
class BooksBorrowedProviderImpl implements AcademicInfoProvider {

    private final BorrowingHistoryInfoMapper borrowingMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            long currentCount = 0;
            String comparison = "0";

            if (StringUtils.hasText(semester)) {
                // 按学期日期范围筛选借阅记录
                String startDate = SemesterDateUtil.getSemesterStartDate(semester);
                String endDate = SemesterDateUtil.getSemesterEndDate(semester);

                if (StringUtils.hasText(startDate) && StringUtils.hasText(endDate)) {
                    // 查询当前学期的借阅数量
                    currentCount = queryBorrowingForDateRange(studentId, startDate, endDate);
                    // 计算与上一学期的借阅数量差异
                    comparison = calculateBorrowingComparison(studentId, semester);
                } else {
                    log.warn("无法解析学期日期范围，学期：{}", semester);
                }
            } else {
                // 总借阅数量查询
                currentCount = queryTotalBorrowing(studentId);
            }

            var booksBorrowedVO = BooksBorrowedVO.builder()
                    .count(currentCount + "本")
                    .comparison(comparison)
                    .build();

            return builder -> builder.booksBorrowedInfo(booksBorrowedVO);
        } catch (Exception e) {
            log.error("获取图书借阅信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorBorrowingVO();
        }
    }

    /**
     * 查询指定日期范围的借阅数量
     */
    private long queryBorrowingForDateRange(String studentId, String startDate, String endDate) {
        return borrowingMapper.countBorrowingByStudentIdAndDateRange(studentId, startDate, endDate);
    }

    /**
     * 查询总借阅数量
     */
    private long queryTotalBorrowing(String studentId) {
        var query = MpQueryUtils.<BorrowingHistoryInfo>lambdaQuery()
                .eq(BorrowingHistoryInfo::getStudentStaffId, studentId)
                .build();

        return borrowingMapper.selectCount(query);
    }

    /**
     * 计算借阅对比
     */
    private String calculateBorrowingComparison(String studentId, String semester) {
        String previousSemester = SemesterDateUtil.getPreviousSemester(semester);
        if (!StringUtils.hasText(previousSemester)) {
            return "0";
        }

        String currentStartDate = SemesterDateUtil.getSemesterStartDate(semester);
        String currentEndDate = SemesterDateUtil.getSemesterEndDate(semester);
        String prevStartDate = SemesterDateUtil.getSemesterStartDate(previousSemester);
        String prevEndDate = SemesterDateUtil.getSemesterEndDate(previousSemester);

        if (!StringUtils.hasText(currentStartDate) || !StringUtils.hasText(currentEndDate) ||
            !StringUtils.hasText(prevStartDate) || !StringUtils.hasText(prevEndDate)) {
            return "0";
        }

        long currentCount = queryBorrowingForDateRange(studentId, currentStartDate, currentEndDate);
        long previousCount = queryBorrowingForDateRange(studentId, prevStartDate, prevEndDate);

        long diff = currentCount - previousCount;
        return diff >= 0 ? "+" + diff : String.valueOf(diff);
    }

    /**
     * 构建错误时的借阅VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorBorrowingVO() {
        var booksBorrowedVO = BooksBorrowedVO.builder()
                .count("0本")
                .comparison("0")
                .build();
        return builder -> builder.booksBorrowedInfo(booksBorrowedVO);
    }
}