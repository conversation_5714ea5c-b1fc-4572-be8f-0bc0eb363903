package com.zkdm.iai.provider.impl.academicinfo.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 学生类型枚举
 * <p>
 * 用于区分不同类型的学生GPA数据获取策略：
 * - WARNING: 预警学生，获取历史GPA数据
 * - EXEMPLARY: 榜样学生，获取未来预测GPA数据
 * - SIMILAR: 相似学生，获取未来预测GPA数据
 * <p>
 * 支持策略模式的实现，每种类型对应不同的数据处理策略
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum StudentType {
    
    /**
     * 预警学生
     * <p>
     * 特点：
     * - 获取从入学到当前学期的历史GPA数据
     * - 用于展示学生的学业发展轨迹
     * - 数据范围：当前学期及之前
     */
    WARNING("预警学生", "获取历史GPA轨迹数据", true),
    
    /**
     * 榜样学生
     * <p>
     * 特点：
     * - 获取当前学期之后的预测GPA数据
     * - 用于展示目标学习轨迹
     * - 数据范围：当前学期之后
     */
    EXEMPLARY("榜样学生", "获取目标GPA轨迹数据", false),
    
    /**
     * 相似学生
     * <p>
     * 特点：
     * - 获取当前学期之后的预测GPA数据
     * - 用于展示参考学习轨迹
     * - 数据范围：当前学期之后
     */
    SIMILAR("相似学生", "获取参考GPA轨迹数据", false);
    
    /**
     * 学生类型名称
     */
    private final String typeName;
    
    /**
     * 学生类型描述
     */
    private final String description;
    
    /**
     * 是否包含历史数据
     * <p>
     * true: 获取当前学期及之前的数据（预警学生）
     * false: 获取当前学期之后的数据（榜样学生、相似学生）
     */
    private final boolean includeHistoricalData;
    
    /**
     * 根据学生类型名称获取枚举值
     * 
     * @param typeName 学生类型名称
     * @return 对应的枚举值，如果未找到则返回null
     */
    public static StudentType fromTypeName(String typeName) {
        for (StudentType type : values()) {
            if (type.getTypeName().equals(typeName)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * 判断是否为预警学生类型
     * 
     * @return true表示是预警学生，false表示不是
     */
    public boolean isWarningStudent() {
        return this == WARNING;
    }
    
    /**
     * 判断是否为参考学生类型（榜样学生或相似学生）
     * 
     * @return true表示是参考学生，false表示不是
     */
    public boolean isReferenceStudent() {
        return this == EXEMPLARY || this == SIMILAR;
    }
}
