package com.zkdm.iai.provider.impl.academicinfo.utils;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 学期计算工具类
 * <p>
 * 提供学期相关的计算和过滤功能，包括：
 * - 根据学生类型过滤学期数据
 * - 学期范围计算
 * - 学期数据验证
 * <p>
 * 支持不同学生类型的数据过滤策略：
 * - 预警学生：当前学期及之前的数据
 * - 榜样/相似学生：当前学期之后的数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public final class SemesterCalculator {
    
    /**
     * 私有构造函数，防止实例化
     */
    private SemesterCalculator() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 根据学生类型和当前学期过滤GPA数据
     * <p>
     * 过滤规则：
     * - 预警学生：返回当前学期及之前的数据
     * - 榜样学生：返回当前学期之后的数据
     * - 相似学生：返回当前学期之后的数据
     * 
     * @param gpaList 原始GPA数据列表
     * @param currentSemester 当前学期，格式如：2023-2024-1
     * @param studentType 学生类型
     * @return 过滤后的GPA数据列表
     */
    public static List<UndergraduateSemesterGpa> filterGpaDataBySemester(
            List<UndergraduateSemesterGpa> gpaList,
            String currentSemester,
            StudentType studentType) {
        
        // 参数验证
        if (CollectionUtils.isEmpty(gpaList)) {
            log.debug("GPA数据列表为空，返回空列表");
            return new ArrayList<>();
        }
        
        if (studentType == null) {
            log.warn("学生类型为null，返回原始数据");
            return new ArrayList<>(gpaList);
        }
        
        // 如果没有指定当前学期，返回所有数据
        if (!StringUtils.hasText(currentSemester)) {
            log.debug("当前学期为空，返回所有数据");
            return new ArrayList<>(gpaList);
        }
        
        log.debug("开始过滤GPA数据：学生类型={}, 当前学期={}, 原始数据量={}", 
                studentType.getTypeName(), currentSemester, gpaList.size());
        
        List<UndergraduateSemesterGpa> filteredList;
        
        if (studentType.isWarningStudent()) {
            // 预警学生：获取当前学期及之前的数据
            filteredList = filterHistoricalData(gpaList, currentSemester);
        } else {
            // 榜样学生和相似学生：获取当前学期之后的数据
            filteredList = filterFutureData(gpaList, currentSemester);
        }
        
        log.debug("GPA数据过滤完成：过滤后数据量={}", filteredList.size());
        return filteredList;
    }
    
    /**
     * 过滤历史数据（当前学期及之前）
     * 
     * @param gpaList GPA数据列表
     * @param currentSemester 当前学期
     * @return 历史数据列表
     */
    private static List<UndergraduateSemesterGpa> filterHistoricalData(
            List<UndergraduateSemesterGpa> gpaList,
            String currentSemester) {
        
        return gpaList.stream()
                .filter(gpa -> isValidSemester(gpa.getAcademicYearSemester()))
                .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) <= 0)
                .collect(Collectors.toList());
    }
    
    /**
     * 过滤未来数据（当前学期之后）
     * 
     * @param gpaList GPA数据列表
     * @param currentSemester 当前学期
     * @return 未来数据列表
     */
    private static List<UndergraduateSemesterGpa> filterFutureData(
            List<UndergraduateSemesterGpa> gpaList,
            String currentSemester) {
        
        return gpaList.stream()
                .filter(gpa -> isValidSemester(gpa.getAcademicYearSemester()))
                .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) > 0)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证学期格式是否有效
     * 
     * @param semester 学期字符串
     * @return true表示有效，false表示无效
     */
    private static boolean isValidSemester(String semester) {
        if (!StringUtils.hasText(semester)) {
            return false;
        }
        
        // 简单的学期格式验证：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }
    
    /**
     * 计算学期范围内的数据统计
     * 
     * @param gpaList GPA数据列表
     * @param startSemester 开始学期（包含）
     * @param endSemester 结束学期（包含）
     * @return 指定范围内的GPA数据列表
     */
    public static List<UndergraduateSemesterGpa> getGpaDataInRange(
            List<UndergraduateSemesterGpa> gpaList,
            String startSemester,
            String endSemester) {
        
        if (CollectionUtils.isEmpty(gpaList)) {
            return new ArrayList<>();
        }
        
        if (!StringUtils.hasText(startSemester) || !StringUtils.hasText(endSemester)) {
            log.warn("学期范围参数无效：开始学期={}, 结束学期={}", startSemester, endSemester);
            return new ArrayList<>();
        }
        
        return gpaList.stream()
                .filter(gpa -> isValidSemester(gpa.getAcademicYearSemester()))
                .filter(gpa -> {
                    String semester = gpa.getAcademicYearSemester();
                    return semester.compareTo(startSemester) >= 0 
                            && semester.compareTo(endSemester) <= 0;
                })
                .collect(Collectors.toList());
    }
    
    /**
     * 获取最新的N个学期数据
     * 
     * @param gpaList GPA数据列表
     * @param count 需要获取的学期数量
     * @return 最新的N个学期数据
     */
    public static List<UndergraduateSemesterGpa> getLatestSemesters(
            List<UndergraduateSemesterGpa> gpaList,
            int count) {
        
        if (CollectionUtils.isEmpty(gpaList) || count <= 0) {
            return new ArrayList<>();
        }
        
        return gpaList.stream()
                .filter(gpa -> isValidSemester(gpa.getAcademicYearSemester()))
                .sorted((a, b) -> b.getAcademicYearSemester().compareTo(a.getAcademicYearSemester()))
                .limit(count)
                .collect(Collectors.toList());
    }
    
    /**
     * 检查是否存在指定学期的数据
     * 
     * @param gpaList GPA数据列表
     * @param targetSemester 目标学期
     * @return true表示存在，false表示不存在
     */
    public static boolean hasSemesterData(
            List<UndergraduateSemesterGpa> gpaList,
            String targetSemester) {
        
        if (CollectionUtils.isEmpty(gpaList) || !StringUtils.hasText(targetSemester)) {
            return false;
        }
        
        return gpaList.stream()
                .anyMatch(gpa -> targetSemester.equals(gpa.getAcademicYearSemester()));
    }
    
    /**
     * 获取数据覆盖的学期范围
     * 
     * @param gpaList GPA数据列表
     * @return 学期范围数组，[0]为最早学期，[1]为最晚学期，如果数据为空则返回null
     */
    public static String[] getSemesterRange(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList)) {
            return null;
        }
        
        List<String> semesters = gpaList.stream()
                .filter(gpa -> isValidSemester(gpa.getAcademicYearSemester()))
                .map(UndergraduateSemesterGpa::getAcademicYearSemester)
                .sorted()
                .collect(Collectors.toList());
        
        if (semesters.isEmpty()) {
            return null;
        }
        
        return new String[]{semesters.get(0), semesters.get(semesters.size() - 1)};
    }
}
