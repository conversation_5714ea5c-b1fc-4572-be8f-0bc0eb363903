package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.AttendanceVO;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.util.function.Consumer;

/**
 * 出勤率信息提供者实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
class AttendanceProviderImpl implements AcademicInfoProvider {

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            // TODO: 考勤表暂未提供，继续使用Mock数据
            log.debug("考勤信息暂时使用Mock数据，学生ID：{}, 学期：{}", studentId, semester);

            var attendanceVO = AttendanceVO.builder()
                    .rate("85%")
                    .comparison("+3")
                    .build();

            return builder -> builder.attendanceInfo(attendanceVO);
        } catch (Exception e) {
            log.error("获取考勤信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorAttendanceVO();
        }
    }

    /**
     * 构建错误时的考勤VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorAttendanceVO() {
        var attendanceVO = AttendanceVO.builder()
                .rate("0%")
                .comparison("0")
                .build();
        return builder -> builder.attendanceInfo(attendanceVO);
    }
}