package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.LibraryAccessVO;
import com.zkdm.iai.mapper.library.LibraryGateAccessLogMapper;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.function.Consumer;

/**
 * 图书馆出入次数信息提供者实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
class LibraryAccessProviderImpl implements AcademicInfoProvider {

    private final LibraryGateAccessLogMapper accessLogMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            long currentCount = 0;
            String comparison = "0";

            if (StringUtils.hasText(semester)) {
                // 按学期日期范围筛选出入记录
                String startDate = SemesterDateUtil.getSemesterStartDate(semester);
                String endDate = SemesterDateUtil.getSemesterEndDate(semester);

                if (StringUtils.hasText(startDate) && StringUtils.hasText(endDate)) {
                    currentCount = queryAccessForDateRange(studentId, startDate, endDate);
                    comparison = calculateAccessComparison(studentId, semester);
                } else {
                    log.warn("无法解析学期日期范围，学期：{}", semester);
                }
            } else {
                // 总出入次数查询
                currentCount = queryTotalAccess(studentId);
            }

            var libraryAccessVO = LibraryAccessVO.builder()
                    .count(currentCount + "次")
                    .comparison(comparison)
                    .build();

            return builder -> builder.libraryAccessInfo(libraryAccessVO);
        } catch (Exception e) {
            log.error("获取图书馆出入信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorAccessVO();
        }
    }

    /**
     * 查询指定日期范围的出入次数
     */
    private long queryAccessForDateRange(String studentId, String startDate, String endDate) {
        return accessLogMapper.countAccessByStudentIdAndDateRange(studentId, startDate, endDate);
    }

    /**
     * 查询总出入次数
     */
    private long queryTotalAccess(String studentId) {
        return accessLogMapper.countTotalAccessByStudentId(studentId);
    }

    /**
     * 计算出入对比
     */
    private String calculateAccessComparison(String studentId, String semester) {
        String previousSemester = SemesterDateUtil.getPreviousSemester(semester);
        if (!StringUtils.hasText(previousSemester)) {
            return "0";
        }

        String currentStartDate = SemesterDateUtil.getSemesterStartDate(semester);
        String currentEndDate = SemesterDateUtil.getSemesterEndDate(semester);
        String prevStartDate = SemesterDateUtil.getSemesterStartDate(previousSemester);
        String prevEndDate = SemesterDateUtil.getSemesterEndDate(previousSemester);

        if (!StringUtils.hasText(currentStartDate) || !StringUtils.hasText(currentEndDate) ||
            !StringUtils.hasText(prevStartDate) || !StringUtils.hasText(prevEndDate)) {
            return "0";
        }

        long currentCount = queryAccessForDateRange(studentId, currentStartDate, currentEndDate);
        long previousCount = queryAccessForDateRange(studentId, prevStartDate, prevEndDate);

        long diff = currentCount - previousCount;
        return diff >= 0 ? "+" + diff : String.valueOf(diff);
    }

    /**
     * 构建错误时的出入VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorAccessVO() {
        var libraryAccessVO = LibraryAccessVO.builder()
                .count("0次")
                .comparison("0")
                .build();
        return builder -> builder.libraryAccessInfo(libraryAccessVO);
    }
}