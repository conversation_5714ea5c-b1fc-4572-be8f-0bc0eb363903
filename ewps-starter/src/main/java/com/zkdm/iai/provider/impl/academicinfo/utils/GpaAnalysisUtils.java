package com.zkdm.iai.provider.impl.academicinfo.utils;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GPA分析工具类
 * <p>
 * 提供高级的GPA数据分析功能，包括：
 * - 趋势分析：计算GPA变化趋势和改进幅度
 * - 统计分析：计算平均值、标准差、变异系数等
 * - 对比分析：比较不同学生的GPA表现
 * - 预测分析：基于历史数据预测未来趋势
 * - 风险评估：评估学业风险等级
 * <p>
 * 该工具类为GPA分析接口提供更深入的数据洞察能力
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public final class GpaAnalysisUtils {
    
    /**
     * GPA风险等级枚举
     */
    public enum RiskLevel {
        LOW("低风险", 3.0, 4.0),
        MEDIUM("中等风险", 2.0, 3.0),
        HIGH("高风险", 1.0, 2.0),
        CRITICAL("严重风险", 0.0, 1.0);
        
        private final String description;
        private final double minGpa;
        private final double maxGpa;
        
        RiskLevel(String description, double minGpa, double maxGpa) {
            this.description = description;
            this.minGpa = minGpa;
            this.maxGpa = maxGpa;
        }
        
        public String getDescription() { return description; }
        public double getMinGpa() { return minGpa; }
        public double getMaxGpa() { return maxGpa; }
    }
    
    /**
     * GPA趋势枚举
     */
    public enum TrendType {
        IMPROVING("上升趋势", "学业表现持续改善"),
        DECLINING("下降趋势", "学业表现持续下滑"),
        STABLE("稳定趋势", "学业表现相对稳定"),
        VOLATILE("波动趋势", "学业表现波动较大");
        
        private final String name;
        private final String description;
        
        TrendType(String name, String description) {
            this.name = name;
            this.description = description;
        }
        
        public String getName() { return name; }
        public String getDescription() { return description; }
    }
    
    /**
     * 私有构造函数，防止实例化
     */
    private GpaAnalysisUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 计算GPA平均值
     * 
     * @param gpaList GPA数据列表
     * @return 平均GPA，如果列表为空则返回0.0
     */
    public static double calculateAverageGpa(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList)) {
            return 0.0;
        }
        
        return gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                .average()
                .orElse(0.0);
    }
    
    /**
     * 计算GPA标准差
     * 
     * @param gpaList GPA数据列表
     * @return GPA标准差
     */
    public static double calculateGpaStandardDeviation(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList) || gpaList.size() < 2) {
            return 0.0;
        }
        
        double average = calculateAverageGpa(gpaList);
        
        double variance = gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .mapToDouble(gpa -> {
                    double diff = gpa.getGpa().doubleValue() - average;
                    return diff * diff;
                })
                .average()
                .orElse(0.0);
        
        return Math.sqrt(variance);
    }
    
    /**
     * 分析GPA趋势
     * 
     * @param gpaList 按时间排序的GPA数据列表
     * @return GPA趋势类型
     */
    public static TrendType analyzeGpaTrend(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList) || gpaList.size() < 3) {
            return TrendType.STABLE;
        }
        
        List<Double> gpaValues = gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .map(gpa -> gpa.getGpa().doubleValue())
                .collect(Collectors.toList());
        
        if (gpaValues.size() < 3) {
            return TrendType.STABLE;
        }
        
        // 计算线性回归斜率
        double slope = calculateLinearRegressionSlope(gpaValues);
        
        // 计算变异系数
        double average = gpaValues.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double stdDev = Math.sqrt(gpaValues.stream()
                .mapToDouble(gpa -> Math.pow(gpa - average, 2))
                .average().orElse(0.0));
        double coefficientOfVariation = average > 0 ? stdDev / average : 0.0;
        
        // 判断趋势类型
        if (coefficientOfVariation > 0.3) {
            return TrendType.VOLATILE;
        } else if (slope > 0.1) {
            return TrendType.IMPROVING;
        } else if (slope < -0.1) {
            return TrendType.DECLINING;
        } else {
            return TrendType.STABLE;
        }
    }
    
    /**
     * 计算线性回归斜率
     * 
     * @param values 数值列表
     * @return 线性回归斜率
     */
    private static double calculateLinearRegressionSlope(List<Double> values) {
        int n = values.size();
        if (n < 2) return 0.0;
        
        double sumX = 0, sumY = 0, sumXY = 0, sumXX = 0;
        
        for (int i = 0; i < n; i++) {
            double x = i + 1; // 时间序列
            double y = values.get(i);
            
            sumX += x;
            sumY += y;
            sumXY += x * y;
            sumXX += x * x;
        }
        
        double denominator = n * sumXX - sumX * sumX;
        if (Math.abs(denominator) < 1e-10) return 0.0;
        
        return (n * sumXY - sumX * sumY) / denominator;
    }
    
    /**
     * 评估学业风险等级
     * 
     * @param currentGpa 当前GPA
     * @param trend 趋势类型
     * @return 风险等级
     */
    public static RiskLevel assessRiskLevel(double currentGpa, TrendType trend) {
        // 基于当前GPA确定基础风险等级
        RiskLevel baseRisk = getRiskLevelByGpa(currentGpa);
        
        // 根据趋势调整风险等级
        switch (trend) {
            case IMPROVING:
                // 上升趋势降低风险等级
                return adjustRiskLevel(baseRisk, -1);
            case DECLINING:
                // 下降趋势提高风险等级
                return adjustRiskLevel(baseRisk, 1);
            case VOLATILE:
                // 波动趋势略微提高风险等级
                return adjustRiskLevel(baseRisk, 0.5);
            default:
                return baseRisk;
        }
    }
    
    /**
     * 根据GPA值确定风险等级
     * 
     * @param gpa GPA值
     * @return 风险等级
     */
    private static RiskLevel getRiskLevelByGpa(double gpa) {
        for (RiskLevel level : RiskLevel.values()) {
            if (gpa >= level.getMinGpa() && gpa < level.getMaxGpa()) {
                return level;
            }
        }
        return RiskLevel.CRITICAL;
    }
    
    /**
     * 调整风险等级
     * 
     * @param currentLevel 当前风险等级
     * @param adjustment 调整幅度（正数提高风险，负数降低风险）
     * @return 调整后的风险等级
     */
    private static RiskLevel adjustRiskLevel(RiskLevel currentLevel, double adjustment) {
        RiskLevel[] levels = RiskLevel.values();
        int currentIndex = Arrays.asList(levels).indexOf(currentLevel);
        
        int newIndex = currentIndex + (int) Math.round(adjustment);
        newIndex = Math.max(0, Math.min(levels.length - 1, newIndex));
        
        return levels[newIndex];
    }
    
    /**
     * 计算改进潜力
     * 
     * @param currentGpa 当前GPA
     * @param targetGpa 目标GPA
     * @param historicalGpaList 历史GPA数据
     * @return 改进潜力评分（0-100）
     */
    public static int calculateImprovementPotential(
            double currentGpa, 
            double targetGpa, 
            List<UndergraduateSemesterGpa> historicalGpaList) {
        
        if (currentGpa >= targetGpa) {
            return 100; // 已达到目标
        }
        
        // 基础潜力：基于当前GPA与目标GPA的差距
        double gapRatio = (targetGpa - currentGpa) / (4.0 - currentGpa);
        int basePotential = (int) ((1 - gapRatio) * 100);
        
        // 历史表现调整
        if (!CollectionUtils.isEmpty(historicalGpaList)) {
            TrendType trend = analyzeGpaTrend(historicalGpaList);
            double maxHistoricalGpa = historicalGpaList.stream()
                    .filter(gpa -> gpa.getGpa() != null)
                    .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                    .max()
                    .orElse(currentGpa);
            
            // 如果历史最高GPA接近或超过目标，提高潜力评分
            if (maxHistoricalGpa >= targetGpa * 0.9) {
                basePotential += 20;
            }
            
            // 根据趋势调整
            switch (trend) {
                case IMPROVING:
                    basePotential += 15;
                    break;
                case DECLINING:
                    basePotential -= 15;
                    break;
                case VOLATILE:
                    basePotential -= 5;
                    break;
            }
        }
        
        return Math.max(0, Math.min(100, basePotential));
    }
    
    /**
     * 生成GPA分析报告
     * 
     * @param gpaList GPA数据列表
     * @return 分析报告字符串
     */
    public static String generateAnalysisReport(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList)) {
            return "无GPA数据可供分析";
        }
        
        StringBuilder report = new StringBuilder();
        
        // 基础统计
        double avgGpa = calculateAverageGpa(gpaList);
        double stdDev = calculateGpaStandardDeviation(gpaList);
        TrendType trend = analyzeGpaTrend(gpaList);
        RiskLevel risk = assessRiskLevel(avgGpa, trend);
        
        report.append("=== GPA分析报告 ===\n");
        report.append(String.format("数据期间：%d个学期\n", gpaList.size()));
        report.append(String.format("平均GPA：%.2f\n", avgGpa));
        report.append(String.format("标准差：%.2f\n", stdDev));
        report.append(String.format("趋势类型：%s\n", trend.getName()));
        report.append(String.format("风险等级：%s\n", risk.getDescription()));
        
        // 最高和最低GPA
        OptionalDouble maxGpa = gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                .max();
        OptionalDouble minGpa = gpaList.stream()
                .filter(gpa -> gpa.getGpa() != null)
                .mapToDouble(gpa -> gpa.getGpa().doubleValue())
                .min();
        
        if (maxGpa.isPresent() && minGpa.isPresent()) {
            report.append(String.format("最高GPA：%.2f\n", maxGpa.getAsDouble()));
            report.append(String.format("最低GPA：%.2f\n", minGpa.getAsDouble()));
            report.append(String.format("GPA波动范围：%.2f\n", maxGpa.getAsDouble() - minGpa.getAsDouble()));
        }
        
        return report.toString();
    }
}
