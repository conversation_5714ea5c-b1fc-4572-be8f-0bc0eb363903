package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.core.constant.AcademicConstants;
import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.GpaVO;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.provider.AcademicInfoProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.Consumer;

/**
 * GPA信息提供者实现类
 * <p>
 * 负责提供学生的GPA相关信息，包括：
 * - 当前学期或最新的GPA值
 * - GPA最大值对比
 * - 与上学期GPA差异计算
 * <p>
 * 数据来源：T_GXXS_BKSXQJDXX（本科生学期绩点信息表）
 * 业务规则：
 * - 支持按学期筛选GPA信息
 * - 自动计算与上学期的GPA差异
 * - GPA保留2位小数显示
 * - 使用常量定义最大GPA值（4.0）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
class GpaProviderImpl implements AcademicInfoProvider {

    private final UndergraduateSemesterGpaMapper semesterGpaMapper;
    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId) {
        return provide(studentId, null);
    }

    @Override
    public Consumer<AcademicInfoVO.AcademicInfoVOBuilder> provide(@NonNull String studentId, @Nullable String semester) {
        try {
            UndergraduateSemesterGpa gpaInfo;
            String currentSemester = semester;

            if (StringUtils.hasText(semester)) {
                gpaInfo = queryGpaForSemester(studentId, semester);
            } else {
                gpaInfo = queryLatestGpa(studentId);
                if (gpaInfo != null) {
                    currentSemester = gpaInfo.getAcademicYearSemester();
                }
            }

            String currentGpa = formatGpa(gpaInfo);
            String comparison = calculateGpaComparison(studentId, currentSemester);

            var gpaVO = GpaVO.builder()
                    .currentGpa(currentGpa)
                    .maxGpa(AcademicConstants.MAX_GPA)
                    .comparison(comparison)
                    .build();

            return builder -> builder.gpaInfo(gpaVO);
        } catch (Exception e) {
            log.error("获取GPA信息失败，学生ID：{}, 学期：{}", studentId, semester, e);
            return buildErrorGpaVO();
        }
    }

    /**
     * 查询指定学期的GPA信息
     */
    private UndergraduateSemesterGpa queryGpaForSemester(String studentId, String semester) {
        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .eq(UndergraduateSemesterGpa::getAcademicYearSemester, semester)
                .build();

        return semesterGpaMapper.selectOne(query);
    }

    /**
     * 查询最新的GPA信息
     */
    private UndergraduateSemesterGpa queryLatestGpa(String studentId) {
        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .orderByDesc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();

        return semesterGpaMapper.selectOne(query);
    }

    /**
     * 计算GPA对比
     */
    private String calculateGpaComparison(String studentId, String currentSemester) {
        if (!StringUtils.hasText(currentSemester)) {
            return "0";
        }

        UndergraduateSemesterGpa currentGpaInfo = queryGpaForSemester(studentId, currentSemester);
        if (currentGpaInfo == null || currentGpaInfo.getGpa() == null) {
            return "0";
        }

        // 查询上一学期的GPA
        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .lt(UndergraduateSemesterGpa::getAcademicYearSemester, currentSemester)
                .orderByDesc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();

        UndergraduateSemesterGpa previousGpaInfo = semesterGpaMapper.selectOne(query);
        if (previousGpaInfo == null || previousGpaInfo.getGpa() == null) {
            return "0";
        }

        BigDecimal diff = currentGpaInfo.getGpa().subtract(previousGpaInfo.getGpa());
        return diff.compareTo(BigDecimal.ZERO) >= 0 ?
                "+" + diff.setScale(2, RoundingMode.HALF_UP) :
                diff.setScale(2, RoundingMode.HALF_UP).toString();
    }

    /**
     * 格式化GPA显示
     */
    private String formatGpa(UndergraduateSemesterGpa gpaInfo) {
        if (gpaInfo != null && gpaInfo.getGpa() != null) {
            return gpaInfo.getGpa().setScale(2, RoundingMode.HALF_UP).toString();
        }
        return "0.00";
    }

    /**
     * 构建错误时的GPA VO
     */
    private Consumer<AcademicInfoVO.AcademicInfoVOBuilder> buildErrorGpaVO() {
        var gpaVO = GpaVO.builder()
                .currentGpa("0.00")
                .maxGpa(AcademicConstants.MAX_GPA)
                .comparison("0")
                .build();
        return builder -> builder.gpaInfo(gpaVO);
    }
}