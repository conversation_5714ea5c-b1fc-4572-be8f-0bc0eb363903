package com.zkdm.iai.provider.impl.academicinfo.strategy;

import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;

import java.util.List;
import java.util.Map;

/**
 * GPA数据获取策略接口
 * <p>
 * 定义了不同类型学生GPA数据获取的统一接口，支持策略模式的实现。
 * 每种学生类型（预警学生、榜样学生、相似学生）都有对应的具体策略实现。
 * <p>
 * 策略模式的优势：
 * - 算法族独立变化：每种学生类型的数据获取逻辑可以独立修改
 * - 开闭原则：对扩展开放，对修改关闭
 * - 单一职责：每个策略只负责一种类型的数据获取
 * - 可测试性：每个策略可以独立测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface GpaDataStrategy {
    
    /**
     * 获取学生类型
     * <p>
     * 用于标识当前策略处理的学生类型，便于工厂模式的实现
     * 
     * @return 学生类型枚举
     */
    StudentType getStudentType();
    
    /**
     * 获取GPA数据
     * <p>
     * 根据不同的学生类型实现不同的数据获取逻辑：
     * - 预警学生：获取历史GPA数据（当前学期及之前）
     * - 榜样学生：获取未来GPA数据（当前学期之后）
     * - 相似学生：获取未来GPA数据（当前学期之后）
     * 
     * @param studentId 学生学号，不能为空
     * @param currentSemester 当前学期，格式如：2023-2024-1，可以为空
     * @param referenceStudentId 参考学生学号，用于学期转换，不能为空
     * @return GPA数据列表，每个Map包含semester和gpa两个键值对
     * @throws IllegalArgumentException 当必要参数为空时抛出
     */
    List<Map<String, String>> getGpaData(String studentId, String currentSemester, String referenceStudentId);
    
    /**
     * 验证策略是否支持指定的学生类型
     * <p>
     * 用于运行时检查策略的适用性
     * 
     * @param studentType 要检查的学生类型
     * @return true表示支持，false表示不支持
     */
    default boolean supports(StudentType studentType) {
        return getStudentType() == studentType;
    }
    
    /**
     * 获取策略描述
     * <p>
     * 用于日志记录和调试，提供策略的可读性描述
     * 
     * @return 策略描述字符串
     */
    default String getStrategyDescription() {
        return getStudentType().getDescription();
    }
}
