package com.zkdm.iai.provider.impl.academicinfo.factory;

import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.strategy.GpaDataStrategy;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;




import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * GPA Provider工厂类
 * <p>
 * 使用工厂模式管理不同类型的GPA数据获取策略，提供统一的策略获取接口。
 * 支持策略的动态注册和查找，便于扩展新的学生类型。
 * <p>
 * 工厂模式的优势：
 * - 封装创建逻辑：客户端无需知道具体策略的创建细节
 * - 类型安全：通过枚举确保策略类型的正确性
 * - 扩展性：新增策略只需实现接口并注册到Spring容器
 * - 性能优化：策略实例缓存，避免重复创建
 * <p>
 * 设计特点：
 * - 自动发现：通过Spring容器自动发现所有策略实现
 * - 类型映射：使用EnumMap提供高效的类型到策略的映射
 * - 异常处理：提供完善的异常处理和日志记录
 * - 验证机制：确保所有必要的策略都已注册
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GpaProviderFactory {
    
    /**
     * 所有GPA数据策略的列表
     * <p>
     * Spring会自动注入所有实现了GpaDataStrategy接口的Bean
     */
    private final List<GpaDataStrategy> strategies;
    
    /**
     * 策略映射表
     * <p>
     * 使用EnumMap提供高效的枚举到策略的映射
     * EnumMap的优势：
     * - 性能优异：基于数组实现，查找时间复杂度O(1)
     * - 类型安全：只能使用指定的枚举类型作为key
     * - 内存效率：比HashMap更节省内存
     */
    private final Map<StudentType, GpaDataStrategy> strategyMap = new EnumMap<>(StudentType.class);
    
    /**
     * 初始化方法
     * <p>
     * 在Bean创建完成后自动执行，用于构建策略映射表
     * 验证所有必要的策略是否都已注册
     */
    @PostConstruct
    public void initializeStrategies() {
        log.info("开始初始化GPA数据获取策略工厂");
        
        if (strategies == null || strategies.isEmpty()) {
            log.error("没有找到任何GPA数据策略实现，请检查策略类是否正确注册到Spring容器");
            throw new IllegalStateException("GPA数据策略列表为空");
        }
        
        // 构建策略映射表
        for (GpaDataStrategy strategy : strategies) {
            StudentType studentType = strategy.getStudentType();
            
            if (studentType == null) {
                log.warn("策略{}返回的学生类型为null，跳过注册", strategy.getClass().getSimpleName());
                continue;
            }
            
            if (strategyMap.containsKey(studentType)) {
                log.warn("发现重复的策略类型{}，原策略：{}，新策略：{}，将使用新策略", 
                        studentType, strategyMap.get(studentType).getClass().getSimpleName(), 
                        strategy.getClass().getSimpleName());
            }
            
            strategyMap.put(studentType, strategy);
            log.debug("注册GPA数据策略：{} -> {}", studentType.getTypeName(), 
                    strategy.getClass().getSimpleName());
        }
        
        // 验证所有必要的策略是否都已注册
        validateRequiredStrategies();
        
        log.info("GPA数据获取策略工厂初始化完成，共注册{}个策略", strategyMap.size());
    }
    
    /**
     * 根据学生类型获取对应的GPA数据策略
     * <p>
     * 这是工厂的核心方法，提供类型安全的策略获取功能
     * 
     * @param studentType 学生类型，不能为null
     * @return 对应的GPA数据策略
     * @throws IllegalArgumentException 当学生类型为null时抛出
     * @throws IllegalStateException 当找不到对应策略时抛出
     */
    public GpaDataStrategy getStrategy(StudentType studentType) {
        if (studentType == null) {
            throw new IllegalArgumentException("学生类型不能为null");
        }
        
        GpaDataStrategy strategy = strategyMap.get(studentType);
        
        if (strategy == null) {
            log.error("没有找到学生类型{}对应的GPA数据策略", studentType.getTypeName());
            throw new IllegalStateException("未找到学生类型 " + studentType.getTypeName() + " 对应的策略");
        }
        
        log.trace("获取GPA数据策略：{} -> {}", studentType.getTypeName(), 
                strategy.getClass().getSimpleName());
        
        return strategy;
    }
    
    /**
     * 安全地获取策略（返回Optional）
     * <p>
     * 提供更安全的策略获取方式，避免异常抛出
     * 
     * @param studentType 学生类型
     * @return 包装在Optional中的策略，如果未找到则返回空Optional
     */
    public Optional<GpaDataStrategy> getStrategyOptional(StudentType studentType) {
        if (studentType == null) {
            return Optional.empty();
        }
        
        return Optional.ofNullable(strategyMap.get(studentType));
    }
    
    /**
     * 检查是否支持指定的学生类型
     * 
     * @param studentType 学生类型
     * @return true表示支持，false表示不支持
     */
    public boolean supportsStudentType(StudentType studentType) {
        return studentType != null && strategyMap.containsKey(studentType);
    }
    
    /**
     * 获取所有支持的学生类型
     * 
     * @return 支持的学生类型集合
     */
    public java.util.Set<StudentType> getSupportedStudentTypes() {
        return strategyMap.keySet();
    }
    
    /**
     * 获取策略统计信息
     * 
     * @return 策略统计信息字符串
     */
    public String getStrategyStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("GPA数据策略统计信息：\n");
        stats.append("总策略数：").append(strategyMap.size()).append("\n");
        
        for (Map.Entry<StudentType, GpaDataStrategy> entry : strategyMap.entrySet()) {
            stats.append("- ").append(entry.getKey().getTypeName())
                 .append("：").append(entry.getValue().getClass().getSimpleName())
                 .append("\n");
        }
        
        return stats.toString();
    }
    
    /**
     * 验证所有必要的策略是否都已注册
     * <p>
     * 确保系统的完整性，所有定义的学生类型都有对应的策略实现
     * 
     * @throws IllegalStateException 当缺少必要策略时抛出
     */
    private void validateRequiredStrategies() {
        for (StudentType studentType : StudentType.values()) {
            if (!strategyMap.containsKey(studentType)) {
                log.error("缺少学生类型{}的GPA数据策略实现", studentType.getTypeName());
                throw new IllegalStateException("缺少学生类型 " + studentType.getTypeName() + " 的策略实现");
            }
        }
        
        log.debug("所有必要的GPA数据策略都已注册");
    }
    
    /**
     * 重新加载策略（用于动态更新）
     * <p>
     * 提供运行时重新加载策略的能力，便于系统维护
     * 注意：这个方法应该谨慎使用，通常只在开发或维护阶段使用
     */
    public synchronized void reloadStrategies() {
        log.info("开始重新加载GPA数据策略");
        
        strategyMap.clear();
        initializeStrategies();
        
        log.info("GPA数据策略重新加载完成");
    }
}
