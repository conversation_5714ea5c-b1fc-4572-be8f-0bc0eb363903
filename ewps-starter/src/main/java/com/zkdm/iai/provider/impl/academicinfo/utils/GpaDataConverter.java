package com.zkdm.iai.provider.impl.academicinfo.utils;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GPA数据转换工具类
 * <p>
 * 提供GPA相关数据的转换和格式化功能，包括：
 * - GPA实体列表转换为Map列表
 * - GPA值格式化
 * - 学期格式转换
 * - 数据验证和清洗
 * <p>
 * 遵循单一职责原则，专门处理GPA数据的转换逻辑
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
public final class GpaDataConverter {
    
    /**
     * 私有构造函数，防止实例化
     */
    private GpaDataConverter() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }
    
    /**
     * 将GPA实体列表转换为Map列表
     * <p>
     * 转换规则：
     * - semester: 学期的中文描述（如：大一上、大一下）
     * - gpa: GPA值的字符串表示（保留1位小数）
     * <p>
     * 数据验证：
     * - 过滤掉无效的GPA记录
     * - 确保学期格式正确
     * - 处理空值和异常情况
     * 
     * @param gpaList GPA实体列表，不能为null
     * @param referenceStudentId 参考学生学号，用于学期转换，不能为空
     * @return 转换后的Map列表，每个Map包含semester和gpa两个键值对
     * @throws IllegalArgumentException 当参数无效时抛出
     */
    public static List<Map<String, String>> convertGpaListToMapList(
            List<UndergraduateSemesterGpa> gpaList, 
            String referenceStudentId) {
        
        // 参数验证
        if (gpaList == null) {
            log.warn("GPA列表为null，返回空列表");
            return new ArrayList<>();
        }
        
        if (referenceStudentId == null || referenceStudentId.trim().isEmpty()) {
            throw new IllegalArgumentException("参考学生学号不能为空");
        }
        
        if (CollectionUtils.isEmpty(gpaList)) {
            log.debug("GPA列表为空，返回空列表");
            return new ArrayList<>();
        }
        
        log.debug("开始转换GPA列表，共{}条记录，参考学生：{}", gpaList.size(), referenceStudentId);
        
        return gpaList.stream()
                .filter(GpaDataConverter::isValidGpaRecord)
                .map(gpa -> convertSingleGpaToMap(gpa, referenceStudentId))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
    
    /**
     * 将单个GPA实体转换为Map
     * 
     * @param gpa GPA实体
     * @param referenceStudentId 参考学生学号
     * @return 转换后的Map，转换失败返回null
     */
    private static Map<String, String> convertSingleGpaToMap(
            UndergraduateSemesterGpa gpa, 
            String referenceStudentId) {
        
        try {
            Map<String, String> map = new HashMap<>();
            
            // 转换学期格式为中文描述
            String semesterDesc = SemesterDateUtil.convertSemesterToChineseDescription(
                    gpa.getAcademicYearSemester(), referenceStudentId);
            map.put("semester", semesterDesc);
            
            // 格式化GPA值
            String gpaValue = formatGpaValue(gpa.getGpa());
            map.put("gpa", gpaValue);
            
            log.trace("转换GPA记录成功：学期={}, GPA={}", semesterDesc, gpaValue);
            return map;
            
        } catch (Exception e) {
            log.warn("转换GPA记录失败：学期={}, GPA={}, 错误={}", 
                    gpa.getAcademicYearSemester(), gpa.getGpa(), e.getMessage());
            return null;
        }
    }
    
    /**
     * 验证GPA记录是否有效
     * 
     * @param gpa GPA记录
     * @return true表示有效，false表示无效
     */
    private static boolean isValidGpaRecord(UndergraduateSemesterGpa gpa) {
        if (gpa == null) {
            log.trace("GPA记录为null，跳过");
            return false;
        }
        
        if (gpa.getAcademicYearSemester() == null || gpa.getAcademicYearSemester().trim().isEmpty()) {
            log.trace("学期信息为空，跳过GPA记录：{}", gpa.getId());
            return false;
        }
        
        // GPA值可以为null或0，这是有效的情况
        return true;
    }
    
    /**
     * 格式化GPA值
     * <p>
     * 格式化规则：
     * - null值转换为"0.0"
     * - 保留1位小数
     * - 使用四舍五入
     * 
     * @param gpa GPA值
     * @return 格式化后的GPA字符串
     */
    public static String formatGpaValue(BigDecimal gpa) {
        if (gpa == null) {
            return "0.0";
        }
        
        try {
            return gpa.setScale(1, BigDecimal.ROUND_HALF_UP).toString();
        } catch (Exception e) {
            log.warn("格式化GPA值失败：{}, 使用默认值0.0", gpa, e);
            return "0.0";
        }
    }
    
    /**
     * 批量格式化GPA值列表
     * 
     * @param gpaValues GPA值列表
     * @return 格式化后的GPA字符串列表
     */
    public static List<String> formatGpaValues(List<BigDecimal> gpaValues) {
        if (CollectionUtils.isEmpty(gpaValues)) {
            return new ArrayList<>();
        }
        
        return gpaValues.stream()
                .map(GpaDataConverter::formatGpaValue)
                .collect(Collectors.toList());
    }
    
    /**
     * 验证并清洗GPA数据列表
     * <p>
     * 清洗规则：
     * - 移除无效记录
     * - 按学期排序
     * - 去重（相同学期保留最新记录）
     * 
     * @param gpaList 原始GPA列表
     * @return 清洗后的GPA列表
     */
    public static List<UndergraduateSemesterGpa> cleanGpaData(List<UndergraduateSemesterGpa> gpaList) {
        if (CollectionUtils.isEmpty(gpaList)) {
            return new ArrayList<>();
        }
        
        return gpaList.stream()
                .filter(GpaDataConverter::isValidGpaRecord)
                .collect(Collectors.toMap(
                        UndergraduateSemesterGpa::getAcademicYearSemester,
                        gpa -> gpa,
                        (existing, replacement) -> {
                            // 如果有重复学期，保留ID较大的（通常是较新的记录）
                            if (existing.getId() != null && replacement.getId() != null) {
                                return existing.getId().compareTo(replacement.getId()) > 0 ? existing : replacement;
                            }
                            return replacement;
                        }
                ))
                .values()
                .stream()
                .sorted(Comparator.comparing(UndergraduateSemesterGpa::getAcademicYearSemester))
                .collect(Collectors.toList());
    }
    
    /**
     * 构建空的GPA数据Map列表
     * <p>
     * 用于异常情况下返回空数据，确保接口稳定性
     * 
     * @return 空的Map列表
     */
    public static List<Map<String, String>> buildEmptyGpaData() {
        log.debug("构建空的GPA数据列表");
        return new ArrayList<>();
    }
    
    /**
     * 验证Map格式的GPA数据是否有效
     * 
     * @param gpaData GPA数据Map
     * @return true表示有效，false表示无效
     */
    public static boolean isValidGpaDataMap(Map<String, String> gpaData) {
        return gpaData != null 
                && gpaData.containsKey("semester") 
                && gpaData.containsKey("gpa")
                && gpaData.get("semester") != null
                && gpaData.get("gpa") != null;
    }
}
