package com.zkdm.iai.provider.impl.academicinfo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * GPA分析配置类
 * <p>
 * 集中管理GPA分析相关的配置参数，支持通过配置文件进行动态调整。
 * 包括风险阈值、默认学生配置、数据处理参数等。
 * <p>
 * 配置特点：
 * - 外部化配置：支持通过application.yml配置
 * - 类型安全：使用强类型配置属性
 * - 默认值：提供合理的默认配置
 * - 热更新：支持配置的动态刷新
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "gpa.analysis")
public class GpaAnalysisConfig {
    
    /**
     * 风险评估配置
     */
    private RiskAssessment riskAssessment = new RiskAssessment();
    
    /**
     * 默认学生配置
     */
    private DefaultStudents defaultStudents = new DefaultStudents();
    
    /**
     * 数据处理配置
     */
    private DataProcessing dataProcessing = new DataProcessing();
    
    /**
     * 趋势分析配置
     */
    private TrendAnalysis trendAnalysis = new TrendAnalysis();
    
    /**
     * 缓存配置
     */
    private Cache cache = new Cache();
    
    /**
     * 风险评估配置类
     */
    @Data
    public static class RiskAssessment {
        /**
         * 低风险GPA阈值
         */
        private BigDecimal lowRiskThreshold = new BigDecimal("3.0");
        
        /**
         * 中等风险GPA阈值
         */
        private BigDecimal mediumRiskThreshold = new BigDecimal("2.0");
        
        /**
         * 高风险GPA阈值
         */
        private BigDecimal highRiskThreshold = new BigDecimal("1.0");
        
        /**
         * 是否启用趋势调整
         */
        private boolean enableTrendAdjustment = true;
        
        /**
         * 趋势调整权重
         */
        private double trendAdjustmentWeight = 0.2;
    }
    
    /**
     * 默认学生配置类
     */
    @Data
    public static class DefaultStudents {
        /**
         * 默认榜样学生映射（专业 -> 学号）
         */
        private Map<String, String> exemplaryStudents = new HashMap<>();
        
        /**
         * 默认相似学生映射（专业 -> 学号）
         */
        private Map<String, String> similarStudents = new HashMap<>();
        
        /**
         * 全局默认榜样学生
         */
        private String globalExemplaryStudent = "201901002";
        
        /**
         * 全局默认相似学生
         */
        private String globalSimilarStudent = "201908015";
        
        /**
         * 是否启用智能匹配
         */
        private boolean enableSmartMatching = false;
        
        public DefaultStudents() {
            // 初始化默认配置
            exemplaryStudents.put("计算机科学与技术", "201901002");
            exemplaryStudents.put("软件工程", "201901003");
            exemplaryStudents.put("信息安全", "201901004");
            
            similarStudents.put("计算机科学与技术", "201908015");
            similarStudents.put("软件工程", "201908016");
            similarStudents.put("信息安全", "201908017");
        }
    }
    
    /**
     * 数据处理配置类
     */
    @Data
    public static class DataProcessing {
        /**
         * GPA精度（小数位数）
         */
        private int gpaPrecision = 1;
        
        /**
         * 最大数据条数限制
         */
        private int maxDataLimit = 100;
        
        /**
         * 是否启用数据清洗
         */
        private boolean enableDataCleaning = true;
        
        /**
         * 是否启用数据验证
         */
        private boolean enableDataValidation = true;
        
        /**
         * 异常GPA值阈值（超过此值视为异常）
         */
        private BigDecimal abnormalGpaThreshold = new BigDecimal("4.5");
        
        /**
         * 最小有效GPA值
         */
        private BigDecimal minValidGpa = new BigDecimal("0.0");
        
        /**
         * 最大有效GPA值
         */
        private BigDecimal maxValidGpa = new BigDecimal("4.0");
    }
    
    /**
     * 趋势分析配置类
     */
    @Data
    public static class TrendAnalysis {
        /**
         * 趋势分析最小数据点数
         */
        private int minDataPoints = 3;
        
        /**
         * 上升趋势斜率阈值
         */
        private double improvingTrendThreshold = 0.1;
        
        /**
         * 下降趋势斜率阈值
         */
        private double decliningTrendThreshold = -0.1;
        
        /**
         * 波动趋势变异系数阈值
         */
        private double volatileTrendThreshold = 0.3;
        
        /**
         * 是否启用高级趋势分析
         */
        private boolean enableAdvancedAnalysis = true;
        
        /**
         * 预测未来学期数
         */
        private int futureSemesterCount = 4;
    }
    
    /**
     * 缓存配置类
     */
    @Data
    public static class Cache {
        /**
         * 是否启用缓存
         */
        private boolean enabled = true;
        
        /**
         * 缓存过期时间（分钟）
         */
        private int expireMinutes = 30;
        
        /**
         * 最大缓存条目数
         */
        private int maxEntries = 1000;
        
        /**
         * 缓存键前缀
         */
        private String keyPrefix = "gpa:analysis:";
    }
    
    /**
     * 获取专业对应的榜样学生
     * 
     * @param major 专业名称
     * @return 榜样学生学号
     */
    public String getExemplaryStudentByMajor(String major) {
        return defaultStudents.getExemplaryStudents()
                .getOrDefault(major, defaultStudents.getGlobalExemplaryStudent());
    }
    
    /**
     * 获取专业对应的相似学生
     * 
     * @param major 专业名称
     * @return 相似学生学号
     */
    public String getSimilarStudentByMajor(String major) {
        return defaultStudents.getSimilarStudents()
                .getOrDefault(major, defaultStudents.getGlobalSimilarStudent());
    }
    
    /**
     * 判断GPA值是否有效
     * 
     * @param gpa GPA值
     * @return true表示有效，false表示无效
     */
    public boolean isValidGpa(BigDecimal gpa) {
        if (gpa == null) {
            return false;
        }
        
        return gpa.compareTo(dataProcessing.getMinValidGpa()) >= 0 
                && gpa.compareTo(dataProcessing.getMaxValidGpa()) <= 0;
    }
    
    /**
     * 判断GPA值是否异常
     * 
     * @param gpa GPA值
     * @return true表示异常，false表示正常
     */
    public boolean isAbnormalGpa(BigDecimal gpa) {
        if (gpa == null) {
            return true;
        }
        
        return gpa.compareTo(dataProcessing.getAbnormalGpaThreshold()) > 0;
    }
    
    /**
     * 获取风险等级描述
     * 
     * @param gpa GPA值
     * @return 风险等级描述
     */
    public String getRiskLevelDescription(BigDecimal gpa) {
        if (gpa == null) {
            return "无数据";
        }
        
        if (gpa.compareTo(riskAssessment.getLowRiskThreshold()) >= 0) {
            return "低风险";
        } else if (gpa.compareTo(riskAssessment.getMediumRiskThreshold()) >= 0) {
            return "中等风险";
        } else if (gpa.compareTo(riskAssessment.getHighRiskThreshold()) >= 0) {
            return "高风险";
        } else {
            return "严重风险";
        }
    }
    
    /**
     * 获取配置摘要信息
     * 
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== GPA分析配置摘要 ===\n");
        summary.append(String.format("风险阈值：低风险>=%s, 中等风险>=%s, 高风险>=%s\n",
                riskAssessment.getLowRiskThreshold(),
                riskAssessment.getMediumRiskThreshold(),
                riskAssessment.getHighRiskThreshold()));
        summary.append(String.format("数据处理：精度=%d位, 最大条数=%d\n",
                dataProcessing.getGpaPrecision(),
                dataProcessing.getMaxDataLimit()));
        summary.append(String.format("趋势分析：最小数据点=%d, 启用高级分析=%s\n",
                trendAnalysis.getMinDataPoints(),
                trendAnalysis.isEnableAdvancedAnalysis()));
        summary.append(String.format("缓存配置：启用=%s, 过期时间=%d分钟\n",
                cache.isEnabled(),
                cache.getExpireMinutes()));
        
        return summary.toString();
    }
}
