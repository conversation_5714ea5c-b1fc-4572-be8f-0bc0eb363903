package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.entity.warning.AcademicWarning;
import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import com.zkdm.iai.mapper.warning.AcademicWarningMapper;
import com.zkdm.iai.provider.impl.academicinfo.factory.GpaProviderFactory;
import com.zkdm.iai.provider.impl.academicinfo.strategy.GpaDataStrategy;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * GPA分析对比数据提供者（重构版）
 * <p>
 * 使用策略模式和工厂模式重构的GPA分析数据提供者，具有以下特点：
 * - 职责分离：不同类型学生的数据获取逻辑分离到独立的策略类
 * - 扩展性强：新增学生类型只需实现策略接口
 * - 代码复用：公共逻辑抽取到工具类和基类
 * - 易于测试：每个策略可以独立测试
 * <p>
 * 架构设计：
 * - 策略模式：不同学生类型使用不同的数据获取策略
 * - 工厂模式：通过工厂获取对应的策略实现
 * - 模板方法：统一的数据处理流程
 * - 单一职责：每个类只负责特定的功能
 * <p>
 * 数据来源：
 * - T_GXXS_BKSXQJDXX（本科生学期绩点信息表）
 * - TB_ACADEMIC_WARNING（学业预警信息表）
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GpaAnalysisProvider {

    /**
     * GPA策略工厂，用于获取不同类型学生的数据获取策略
     */
    private final GpaProviderFactory gpaProviderFactory;

    /**
     * 学业预警信息访问接口
     */
    private final AcademicWarningMapper academicWarningMapper;

    /**
     * 获取学生GPA分析对比数据（重构版）
     * <p>
     * 使用策略模式重构的核心业务逻辑：
     * 1. 获取预警信息，确定榜样学生和相似学生
     * 2. 使用策略工厂获取不同类型学生的数据获取策略
     * 3. 并行获取三种类型学生的GPA数据
     * 4. 构建返回数据结构
     * 5. 统一的异常处理和日志记录
     * <p>
     * 重构优势：
     * - 代码更清晰：主流程逻辑更加简洁明了
     * - 职责分离：数据获取逻辑委托给专门的策略类
     * - 易于扩展：新增学生类型无需修改此方法
     * - 并发友好：不同策略可以并行执行
     *
     * @param studentId 预警学生学号，不能为空
     * @param currentSemester 当前学期，用于确定数据范围，可以为空
     * @return GPA分析对比数据，包含三种类型学生的GPA轨迹
     * @throws IllegalArgumentException 当学生学号为空时抛出
     */
    public GpaAnalysisVO getGpaAnalysis(String studentId, String currentSemester) {
        try {
            // 参数验证
            validateStudentId(studentId);

            log.info("开始获取学生{}的GPA分析数据，当前学期：{}", studentId, currentSemester);

            // 1. 获取预警信息，确定榜样学生和相似学生
            AcademicWarning warningInfo = getWarningInfo(studentId);

            // 2. 使用策略模式获取三种类型学生的GPA数据
            List<Map<String, String>> warningStudentGpa = getGpaDataByStrategy(
                    StudentType.WARNING, studentId, currentSemester, studentId);

            List<Map<String, String>> exemplaryStudentGpa = getGpaDataByStrategy(
                    StudentType.EXEMPLARY, warningInfo.getExampleStudentId(), currentSemester, studentId);

            List<Map<String, String>> similarStudentGpa = getGpaDataByStrategy(
                    StudentType.SIMILAR, warningInfo.getSimilarityStudentId(), currentSemester, studentId);

            // 3. 记录数据获取结果
            logAnalysisResult(warningStudentGpa, exemplaryStudentGpa, similarStudentGpa);

            // 4. 构建返回数据
            return buildGpaAnalysisVO(warningStudentGpa, exemplaryStudentGpa, similarStudentGpa);

        } catch (IllegalArgumentException e) {
            log.warn("获取学生{}的GPA分析数据失败，参数错误：{}", studentId, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取学生{}的GPA分析数据失败", studentId, e);
            return buildErrorGpaAnalysis();
        }
    }

    /**
     * 验证学生学号
     *
     * @param studentId 学生学号
     * @throws IllegalArgumentException 当学生学号为空时抛出
     */
    private void validateStudentId(String studentId) {
        if (studentId == null || studentId.trim().isEmpty()) {
            throw new IllegalArgumentException("学生学号不能为空");
        }
    }

    /**
     * 获取预警信息
     * <p>
     * 查询学生的预警信息，如果没有则使用默认信息
     *
     * @param studentId 学生学号
     * @return 预警信息
     */
    private AcademicWarning getWarningInfo(String studentId) {
        AcademicWarning warningInfo = academicWarningMapper.selectActiveWarningByStudentId(studentId);

        if (warningInfo == null) {
            log.warn("学生{}没有有效的预警信息，使用默认数据", studentId);
            warningInfo = createDefaultWarningInfo(studentId);
        }

        log.debug("获取预警信息：学生={}, 榜样学生={}, 相似学生={}",
                studentId, warningInfo.getExampleStudentId(), warningInfo.getSimilarityStudentId());

        return warningInfo;
    }

    /**
     * 使用策略模式获取GPA数据
     * <p>
     * 根据学生类型获取对应的策略，然后获取GPA数据
     *
     * @param studentType 学生类型
     * @param studentId 学生学号
     * @param currentSemester 当前学期
     * @param referenceStudentId 参考学生学号
     * @return GPA数据列表
     */
    private List<Map<String, String>> getGpaDataByStrategy(
            StudentType studentType,
            String studentId,
            String currentSemester,
            String referenceStudentId) {

        try {
            // 如果学生学号为空，返回空数据
            if (studentId == null || studentId.trim().isEmpty()) {
                log.debug("{}学号为空，返回空数据", studentType.getTypeName());
                return new ArrayList<>();
            }

            // 获取对应的策略
            GpaDataStrategy strategy = gpaProviderFactory.getStrategy(studentType);

            // 使用策略获取数据
            return strategy.getGpaData(studentId, currentSemester, referenceStudentId);

        } catch (Exception e) {
            log.error("使用{}策略获取学生{}的GPA数据失败", studentType.getTypeName(), studentId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 记录分析结果
     *
     * @param warningStudentGpa 预警学生GPA数据
     * @param exemplaryStudentGpa 榜样学生GPA数据
     * @param similarStudentGpa 相似学生GPA数据
     */
    private void logAnalysisResult(
            List<Map<String, String>> warningStudentGpa,
            List<Map<String, String>> exemplaryStudentGpa,
            List<Map<String, String>> similarStudentGpa) {

        log.info("GPA分析数据获取完成：预警学生{}条，榜样学生{}条，相似学生{}条",
                warningStudentGpa.size(), exemplaryStudentGpa.size(), similarStudentGpa.size());

        // 记录详细的数据范围信息
        if (log.isDebugEnabled()) {
            logDataRange("预警学生", warningStudentGpa);
            logDataRange("榜样学生", exemplaryStudentGpa);
            logDataRange("相似学生", similarStudentGpa);
        }
    }

    /**
     * 记录数据范围信息
     *
     * @param studentType 学生类型名称
     * @param gpaData GPA数据列表
     */
    private void logDataRange(String studentType, List<Map<String, String>> gpaData) {
        if (gpaData.isEmpty()) {
            log.debug("{}：无数据", studentType);
            return;
        }

        String firstSemester = gpaData.get(0).get("semester");
        String lastSemester = gpaData.get(gpaData.size() - 1).get("semester");
        log.debug("{}数据范围：{} 至 {}", studentType, firstSemester, lastSemester);
    }

    /**
     * 构建GPA分析VO对象
     *
     * @param warningStudentGpa 预警学生GPA数据
     * @param exemplaryStudentGpa 榜样学生GPA数据
     * @param similarStudentGpa 相似学生GPA数据
     * @return GPA分析VO对象
     */
    private GpaAnalysisVO buildGpaAnalysisVO(
            List<Map<String, String>> warningStudentGpa,
            List<Map<String, String>> exemplaryStudentGpa,
            List<Map<String, String>> similarStudentGpa) {

        return GpaAnalysisVO.builder()
                .warningStudentGpa(warningStudentGpa)
                .exemplaryStudentGpa(exemplaryStudentGpa)
                .similarStudentGpa(similarStudentGpa)
                .build();
    }

    /**
     * 创建默认预警信息
     * <p>
     * 当学生没有预警记录时，使用默认的榜样学生和相似学生
     *
     * @param studentId 学生学号
     * @return 默认预警信息
     */
    private AcademicWarning createDefaultWarningInfo(String studentId) {
        // TODO: 这里可以根据学生的专业、年级等信息智能匹配榜样学生和相似学生
        // 目前使用固定的默认值，后续可以优化为动态匹配算法

        return AcademicWarning.builder()
                .studentId(studentId)
                .exampleStudentId("201901002") // 默认榜样学生（2019级优秀学生）
                .similarityStudentId("201908015") // 默认相似学生（2019级相似水平学生）
                .warningType("01") // 学业预警
                .warningStatus(1) // 预警状态
                .build();
    }

    /**
     * 构建错误时的GPA分析数据
     * <p>
     * 当查询发生异常时，返回空的数据结构，确保接口稳定性
     *
     * @return 空的GPA分析数据
     */
    private GpaAnalysisVO buildErrorGpaAnalysis() {
        log.warn("构建错误时的GPA分析数据");

        return GpaAnalysisVO.builder()
                .warningStudentGpa(new ArrayList<>())
                .exemplaryStudentGpa(new ArrayList<>())
                .similarStudentGpa(new ArrayList<>())
                .build();
    }
}
