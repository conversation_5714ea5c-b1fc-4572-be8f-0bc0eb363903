package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import com.zkdm.iai.domain.entity.warning.AcademicWarning;
import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import com.zkdm.iai.mapper.student.UndergraduateSemesterGpaMapper;
import com.zkdm.iai.mapper.warning.AcademicWarningMapper;
import com.zkdm.iai.mybatis.utils.MpQueryUtils;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * GPA分析对比数据提供者
 * <p>
 * 负责提供学生历年GPA信息对比分析数据，包括：
 * - 预警学生的历史GPA轨迹
 * - 榜样学生的GPA参考轨迹
 * - 相似学生的GPA参考轨迹
 * <p>
 * 数据来源：
 * - T_GXXS_BKSXQJDXX（本科生学期绩点信息表）
 * - TB_ACADEMIC_WARNING（学业预警信息表）
 * <p>
 * 业务规则：
 * - 预警学生：返回从入学到当前学期的所有GPA数据
 * - 榜样学生：返回当前学期之后的预测轨迹数据
 * - 相似学生：返回当前学期之后的预测轨迹数据
 * - 学期格式自动转换为中文描述（如：大一上、大一下）
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GpaAnalysisProvider {

    private final UndergraduateSemesterGpaMapper semesterGpaMapper;
    private final AcademicWarningMapper academicWarningMapper;

    /**
     * 获取学生GPA分析对比数据
     * <p>
     * 核心业务逻辑：
     * 1. 查询预警学生的历史GPA数据
     * 2. 根据预警信息获取榜样学生和相似学生信息
     * 3. 查询榜样学生和相似学生的GPA数据
     * 4. 转换学期格式为中文描述
     * 5. 构建返回数据结构
     * 
     * @param studentId 预警学生学号
     * @param currentSemester 当前学期，用于确定数据范围
     * @return GPA分析对比数据
     */
    public GpaAnalysisVO getGpaAnalysis(String studentId, String currentSemester) {
        try {
            log.debug("开始获取学生{}的GPA分析数据，当前学期：{}", studentId, currentSemester);

            // 1. 获取预警学生的历史GPA数据
            List<Map<String, String>> warningStudentGpa = getWarningStudentGpaData(studentId, currentSemester);

            // 2. 查询预警信息，获取榜样学生和相似学生
            AcademicWarning warningInfo = academicWarningMapper.selectActiveWarningByStudentId(studentId);
            if (warningInfo == null) {
                log.warn("学生{}没有有效的预警信息，使用默认数据", studentId);
                warningInfo = getDefaultWarningInfo(studentId);
            }

            // 3. 获取榜样学生的GPA数据（当前学期之后的预测数据）
            List<Map<String, String>> exemplaryStudentGpa = getExemplaryStudentGpaData(
                    warningInfo.getExampleStudentId(), currentSemester, studentId);

            // 4. 获取相似学生的GPA数据（当前学期之后的预测数据）
            List<Map<String, String>> similarStudentGpa = getSimilarStudentGpaData(
                    warningInfo.getSimilarityStudentId(), currentSemester, studentId);

            log.debug("GPA分析数据获取完成：预警学生{}条，榜样学生{}条，相似学生{}条", 
                    warningStudentGpa.size(), exemplaryStudentGpa.size(), similarStudentGpa.size());

            return GpaAnalysisVO.builder()
                    .warningStudentGpa(warningStudentGpa)
                    .exemplaryStudentGpa(exemplaryStudentGpa)
                    .similarStudentGpa(similarStudentGpa)
                    .build();

        } catch (Exception e) {
            log.error("获取学生{}的GPA分析数据失败", studentId, e);
            return buildErrorGpaAnalysis();
        }
    }

    /**
     * 获取预警学生的历史GPA数据
     * <p>
     * 查询该学生从入学到当前学期的所有GPA记录
     * 
     * @param studentId 学生学号
     * @param currentSemester 当前学期
     * @return 历史GPA数据列表
     */
    private List<Map<String, String>> getWarningStudentGpaData(String studentId, String currentSemester) {
        log.debug("查询预警学生{}的历史GPA数据", studentId);

        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, studentId)
                .orderByAsc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();

        List<UndergraduateSemesterGpa> gpaList = semesterGpaMapper.selectList(query);

        // 如果指定了当前学期，只返回当前学期及之前的数据
        if (StringUtils.hasText(currentSemester)) {
            gpaList = gpaList.stream()
                    .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) <= 0)
                    .collect(Collectors.toList());
        }

        return convertGpaListToMapList(gpaList, studentId);
    }

    /**
     * 获取榜样学生的GPA数据
     * <p>
     * 查询榜样学生的GPA记录，仅返回当前学期之后的数据作为目标轨迹
     * 
     * @param exemplaryStudentId 榜样学生学号
     * @param currentSemester 当前学期
     * @param referenceStudentId 参考学生学号（用于学期转换）
     * @return 榜样学生GPA数据列表
     */
    private List<Map<String, String>> getExemplaryStudentGpaData(String exemplaryStudentId, 
                                                                String currentSemester, 
                                                                String referenceStudentId) {
        if (!StringUtils.hasText(exemplaryStudentId)) {
            log.debug("榜样学生学号为空，返回空数据");
            return new ArrayList<>();
        }

        log.debug("查询榜样学生{}的GPA数据", exemplaryStudentId);

        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, exemplaryStudentId)
                .orderByAsc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();

        List<UndergraduateSemesterGpa> gpaList = semesterGpaMapper.selectList(query);

        // 只返回当前学期之后的数据
        if (StringUtils.hasText(currentSemester)) {
            gpaList = gpaList.stream()
                    .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) > 0)
                    .collect(Collectors.toList());
        }

        return convertGpaListToMapList(gpaList, referenceStudentId);
    }

    /**
     * 获取相似学生的GPA数据
     * <p>
     * 查询相似学生的GPA记录，仅返回当前学期之后的数据作为参考轨迹
     * 
     * @param similarStudentId 相似学生学号
     * @param currentSemester 当前学期
     * @param referenceStudentId 参考学生学号（用于学期转换）
     * @return 相似学生GPA数据列表
     */
    private List<Map<String, String>> getSimilarStudentGpaData(String similarStudentId, 
                                                              String currentSemester, 
                                                              String referenceStudentId) {
        if (!StringUtils.hasText(similarStudentId)) {
            log.debug("相似学生学号为空，返回空数据");
            return new ArrayList<>();
        }

        log.debug("查询相似学生{}的GPA数据", similarStudentId);

        var query = MpQueryUtils.<UndergraduateSemesterGpa>lambdaQuery()
                .eq(UndergraduateSemesterGpa::getStudentId, similarStudentId)
                .orderByAsc(UndergraduateSemesterGpa::getAcademicYearSemester)
                .build();

        List<UndergraduateSemesterGpa> gpaList = semesterGpaMapper.selectList(query);

        // 只返回当前学期之后的数据
        if (StringUtils.hasText(currentSemester)) {
            gpaList = gpaList.stream()
                    .filter(gpa -> gpa.getAcademicYearSemester().compareTo(currentSemester) > 0)
                    .collect(Collectors.toList());
        }

        return convertGpaListToMapList(gpaList, referenceStudentId);
    }

    /**
     * 将GPA实体列表转换为Map列表
     * <p>
     * 转换规则：
     * - semester: 学期的中文描述（如：大一上、大一下）
     * - gpa: GPA值的字符串表示
     * 
     * @param gpaList GPA实体列表
     * @param referenceStudentId 参考学生学号（用于学期转换）
     * @return Map列表
     */
    private List<Map<String, String>> convertGpaListToMapList(List<UndergraduateSemesterGpa> gpaList, 
                                                             String referenceStudentId) {
        return gpaList.stream()
                .map(gpa -> {
                    Map<String, String> map = new HashMap<>();
                    
                    // 转换学期格式为中文描述
                    String semesterDesc = SemesterDateUtil.convertSemesterToChineseDescription(
                            gpa.getAcademicYearSemester(), referenceStudentId);
                    map.put("semester", semesterDesc);
                    
                    // 格式化GPA值
                    String gpaValue = formatGpaValue(gpa.getGpa());
                    map.put("gpa", gpaValue);
                    
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 格式化GPA值
     * <p>
     * 将BigDecimal类型的GPA值格式化为字符串，保留1位小数
     * 
     * @param gpa GPA值
     * @return 格式化后的GPA字符串
     */
    private String formatGpaValue(BigDecimal gpa) {
        if (gpa == null) {
            return "0.0";
        }
        return gpa.setScale(1, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 获取默认预警信息
     * <p>
     * 当学生没有预警记录时，使用默认的榜样学生和相似学生
     * 
     * @param studentId 学生学号
     * @return 默认预警信息
     */
    private AcademicWarning getDefaultWarningInfo(String studentId) {
        return AcademicWarning.builder()
                .studentId(studentId)
                .exampleStudentId("202209001") // 默认榜样学生
                .similarityStudentId("202209002") // 默认相似学生
                .build();
    }

    /**
     * 构建错误时的GPA分析数据
     * <p>
     * 当查询发生异常时，返回空的数据结构，确保接口稳定性
     * 
     * @return 空的GPA分析数据
     */
    private GpaAnalysisVO buildErrorGpaAnalysis() {
        log.warn("构建错误时的GPA分析数据");
        
        return GpaAnalysisVO.builder()
                .warningStudentGpa(new ArrayList<>())
                .exemplaryStudentGpa(new ArrayList<>())
                .similarStudentGpa(new ArrayList<>())
                .build();
    }
}
