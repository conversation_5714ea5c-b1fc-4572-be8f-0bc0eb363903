package com.zkdm.iai.controller.warning;

import com.zkdm.iai.core.domain.R;
import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import com.zkdm.iai.service.IGpaAnalysisService;
import com.zkdm.iai.service.academic.IAcademicWarningService;
import com.zkdm.iai.util.SemesterDateUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * Controller: 用于处理学业相关的预警信息。
 */
@Tag(name = "学业预警", description = "用于获取学生学业预警信息的API")
@RestController
@RequestMapping("/api/v1/warnings/academic")
@RequiredArgsConstructor
@Slf4j
public class AcademicWarningController {

    private final IAcademicWarningService academicWarningService;
    private final IGpaAnalysisService gpaAnalysisService;

    /**
     * Gets student academic info.
     *
     * @param studentId the student id
     * @param semester  the semester (optional)
     * @param timestamp the timestamp (optional, for determining current semester)
     * @return the student academic info
     */
    @Operation(summary = "获取学生学业信息",
            description = "根据学号获取学生的学业信息，包括学分、GPA、考勤、获奖等。支持通过学期参数或时间戳参数筛选数据")
    @GetMapping("/{studentId}")
    public R<AcademicInfoVO> getAcademicInfo(
            @Parameter(description = "学生学号", required = true, example = "202310001")
            @PathVariable String studentId,
            @Parameter(description = "学期筛选（可选）", required = false, example = "2023-2024-1")
            @RequestParam(required = false) String semester,
            @Parameter(description = "时间戳（可选，用于判断当前学期）", required = false, example = "1703980800000")
            @RequestParam(required = false) Long timestamp) {
        try {
            // 优先使用semester参数，如果没有则尝试从timestamp计算学期
            String targetSemester = semester;
            if (targetSemester == null && timestamp != null) {
                targetSemester = SemesterDateUtil.getCurrentSemesterByTimestamp(timestamp);
                log.debug("根据时间戳{}计算得到学期：{}", timestamp, targetSemester);
            }

            AcademicInfoVO academicInfo = academicWarningService.getAcademicInfo(studentId, targetSemester);
            return R.ok("查询成功", academicInfo);
        } catch (IllegalArgumentException e) {
            return R.fail(e.getMessage());
        } catch (Exception e) {
            log.error("获取学生{}的学业信息失败，学期：{}，时间戳：{}", studentId, semester, timestamp, e);
            return R.fail("获取学业信息失败：" + e.getMessage());
        }
    }

    /**
     * GET /api/v1/warnings/academic/{studentId}/gpa-analysis
     * 获取指定学生的详细学业指标。
     *
     * @param studentId 学生的唯一ID（学号）。
     * @param timestamp 当前时间戳（可选），用于确定当前学期
     * @return 包含学生GPA分析对比数据的响应实体。
     */
    @Operation(summary = "获取学生历年GPA信息对比分析",
               description = "获取指定预警学生的GPA趋势对比分析，包括预警学生本人、榜样学生和相似学生的GPA历史数据。")
    @GetMapping("/{studentId}/gpa-analysis")
    public R<GpaAnalysisVO> getGpaAnalysis(
            @Parameter(description = "学生的唯一ID（学号）", required = true, example = "202310001")
            @PathVariable String studentId,
            @Parameter(description = "当前时间戳（可选，用于判断当前学期）", required = false, example = "1703980800000")
            @RequestParam(required = false) Long timestamp) {

        try {
            GpaAnalysisVO gpaAnalysis = gpaAnalysisService.getGpaAnalysis(studentId, timestamp);
            return R.ok("GPA分析数据获取成功", gpaAnalysis);
        } catch (IllegalArgumentException e) {
            log.warn("获取GPA分析数据失败，参数错误：{}", e.getMessage());
            return R.fail(e.getMessage());
        } catch (Exception e) {
            // 添加详细的错误日志
            log.error("获取学生{}的GPA分析数据失败，时间戳：{}", studentId, timestamp, e);
            return R.fail("获取GPA分析数据失败: " + e.getMessage());
        }
    }

}