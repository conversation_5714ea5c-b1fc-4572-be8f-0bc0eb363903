package com.zkdm.iai.mapper.warning;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.entity.warning.AcademicWarning;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 学业预警信息Mapper接口
 * <p>
 * 提供学业预警信息的数据访问功能，包括：
 * - 查询预警学生信息
 * - 获取相似学生和榜样学生信息
 * - 预警状态管理
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface AcademicWarningMapper extends BaseMapper<AcademicWarning> {

    /**
     * 根据学生学号查询有效的学业预警信息
     * <p>
     * 查询条件：
     * - 学生学号匹配
     * - 预警类型为学业预警（01）
     * - 预警状态为有效（1）
     * 
     * @param studentId 学生学号
     * @return 学业预警信息，如果没有有效预警记录则返回null
     */
    AcademicWarning selectActiveWarningByStudentId(@Param("studentId") String studentId);

    /**
     * 根据学生学号查询最新的学业预警信息
     * <p>
     * 不限制预警状态，返回最新的一条预警记录
     * 用于获取历史预警信息或已解除的预警信息
     * 
     * @param studentId 学生学号
     * @return 最新的学业预警信息，如果没有记录则返回null
     */
    AcademicWarning selectLatestWarningByStudentId(@Param("studentId") String studentId);
}
