package com.zkdm.iai.mapper.library;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.entity.library.LibraryGateAccessLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 图书馆闸机通行信息Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface LibraryGateAccessLogMapper extends BaseMapper<LibraryGateAccessLog> {

    /**
     * 统计学生在指定学期的图书馆出入次数
     *
     * @param studentId 学生学号
     * @param startDate 学期开始日期
     * @param endDate 学期结束日期
     * @return 出入次数
     */
    @Select("""
        SELECT COUNT(*)
        FROM T_GXTS_TSGZJTXXX
        WHERE XGH = #{studentId}
        AND TXSJ >= #{startDate}
        AND TXSJ <= #{endDate}
        """)
    long countAccessByStudentIdAndDateRange(@Param("studentId") String studentId,
                                          @Param("startDate") String startDate,
                                          @Param("endDate") String endDate);

    /**
     * 统计学生的总图书馆出入次数
     *
     * @param studentId 学生学号
     * @return 总出入次数
     */
    @Select("""
        SELECT COUNT(*)
        FROM T_GXTS_TSGZJTXXX
        WHERE XGH = #{studentId}
        """)
    long countTotalAccessByStudentId(@Param("studentId") String studentId);
}
