package com.zkdm.iai.mapper.student;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.entity.student.UndergraduateSemesterGpa;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 本科生学期绩点信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface UndergraduateSemesterGpaMapper extends BaseMapper<UndergraduateSemesterGpa> {
    
    /**
     * 获取学生所有学期的GPA信息
     *
     * @param studentId 学生学号
     * @return 学期GPA信息列表
     */
    List<UndergraduateSemesterGpa> selectByStudentId(@Param("studentId") String studentId);
    
    /**
     * 获取学生指定学期的GPA信息
     *
     * @param studentId 学生学号
     * @param semester 学期
     * @return 学期GPA信息
     */
    UndergraduateSemesterGpa selectByStudentIdAndSemester(@Param("studentId") String studentId,
                                                          @Param("semester") String semester);
    
    /**
     * 获取学生当前已获得的总学分
     *
     * @param studentId 学生学号
     * @return 总学分
     */
    BigDecimal selectTotalPassedCredits(@Param("studentId") String studentId);
    
    /**
     * 获取学生最新的GPA信息
     *
     * @param studentId 学生学号
     * @return 最新的学期GPA信息
     */
    UndergraduateSemesterGpa selectLatestByStudentId(@Param("studentId") String studentId);

    /**
     * 获取学生指定学期的总学分
     *
     * @param studentId 学生学号
     * @param semester 学期
     * @return 该学期的总学分
     */
    BigDecimal selectPassedCreditsBySemester(@Param("studentId") String studentId,
                                           @Param("semester") String semester);

    /**
     * 获取学生上一个学期的GPA信息（用于对比）
     *
     * @param studentId 学生学号
     * @param currentSemester 当前学期
     * @return 上一学期的GPA信息
     */
    UndergraduateSemesterGpa selectPreviousSemesterGpa(@Param("studentId") String studentId,
                                                      @Param("currentSemester") String currentSemester);
}
