package com.zkdm.iai.mapper.library;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zkdm.iai.domain.entity.library.BorrowingHistoryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * Mapper接口: 借阅历史信息 (BorrowingHistoryInfo)
 *
 * <AUTHOR>
 */
@Mapper
public interface BorrowingHistoryInfoMapper extends BaseMapper<BorrowingHistoryInfo> {

    /**
     * 统计学生在指定日期范围内的借阅次数
     *
     * @param studentId 学生学号
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 借阅次数
     */
    @Select("""
        SELECT COUNT(*)
        FROM T_GXTS_JYLSXX
        WHERE XGH = #{studentId}
        AND JYSJ >= #{startDate}
        AND JYSJ <= #{endDate}
        """)
    long countBorrowingByStudentIdAndDateRange(@Param("studentId") String studentId,
                                             @Param("startDate") String startDate,
                                             @Param("endDate") String endDate);
}