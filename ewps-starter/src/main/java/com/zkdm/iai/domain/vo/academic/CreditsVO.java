package com.zkdm.iai.domain.vo.academic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "已获学分VO")
public class CreditsVO {
    @Schema(description = "当前已获学分", example = "44.5")
    private String currentCredits;
    
    @Schema(description = "毕业要求总学分", example = "166")
    private String totalCredits;

    @Schema(description = "与上学期对比", example = "+10")
    private String comparison;
}