package com.zkdm.iai.domain.vo.academic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
@Schema(description = "GPA信息VO")
public class GpaVO {
    @Schema(description = "当前GPA", example = "3.5")
    private String currentGpa;
    
    @Schema(description = "GPA满分值", example = "4")
    private String maxGpa;

    @Schema(description = "与上学期对比", example = "+10")
    private String comparison;
}