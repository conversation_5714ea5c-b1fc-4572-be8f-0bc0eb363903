package com.zkdm.iai.domain.entity.warn;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：学业预警信息表 (TB_ACADEMIC_WARNING)
 * <AUTHOR>
 */
@Schema(description = "学业预警信息表")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("TB_ACADEMIC_WARNING")
public class AcademicWarning {

    /**
     * 唯一标识
     */
    @Schema(description = "唯一标识", example = "WARN_001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 预警学生学号(外键)
     */
    @Schema(description = "预警学生学号(外键)", example = "202211001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 预警类型码(学业预警: 01)
     */
    @Schema(description = "预警类型码(学业预警: 01)", example = "01")
    @TableField("WARNING_TYPE")
    private String warningType;

    /**
     * 预警状态码(预警: 1, 解除: 0)
     */
    @Schema(description = "预警状态码(预警: 1, 解除: 0)", example = "1")
    @TableField("WARNING_STATUS")
    private Integer warningStatus;

    /**
     * 算法预测相似同学的学号
     */
    @Schema(description = "算法预测相似同学的学号", example = "202211005")
    @TableField("SIMILARITY_STUDENT_XH")
    private String similarityStudentId;

    /**
     * 算法预测榜样同学的学号
     */
    @Schema(description = "算法预测榜样同学的学号", example = "202201008")
    @TableField("EXAMPLE_STUDENT_XH")
    private String exampleStudentId;

    /**
     * 榜样差异描述
     */
    @Schema(description = "榜样差异描述", example = "榜样同学平均绩点3.9，您当前为2.4；榜样同学无挂科记录。")
    @TableField("EXAMPLE_DIFFERENCES")
    private String exampleDifferences;

    /**
     * 指导建议描述
     */
    @Schema(description = "指导建议描述", example = "建议重点复习《高等数学》，每周至少保证10小时的专业课学习时间。")
    @TableField("GUIDANCE")
    private String guidance;
}