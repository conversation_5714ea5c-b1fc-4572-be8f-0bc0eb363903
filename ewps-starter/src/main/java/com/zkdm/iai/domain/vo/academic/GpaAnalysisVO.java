package com.zkdm.iai.domain.vo.academic;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * GPA分析对比VO类
 * <p>
 * 用于返回学生历年GPA信息对比分析数据，包括：
 * - 预警学生的历史GPA数据
 * - 榜样学生的GPA轨迹数据
 * - 相似学生的GPA轨迹数据
 * <p>
 * 数据格式说明：
 * - 每个List包含多个Map，每个Map代表一个学期的数据
 * - Map的key包括："semester"（学期描述）、"gpa"（GPA值）
 * - 学期描述格式：大一上、大一下、大二上、大二下等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "GPA分析对比数据")
public class GpaAnalysisVO {

    /**
     * 预警学生GPA数据
     * <p>
     * 包含该学生从大一入学到当前学期的所有GPA记录
     * 用于展示学生的历史学业表现轨迹
     */
    @Schema(description = "预警学生GPA数据", example = "[{\"semester\":\"大一上\",\"gpa\":\"3.1\"},{\"semester\":\"大一下\",\"gpa\":\"2.8\"}]")
    private List<Map<String, String>> warningStudentGpa;

    /**
     * 榜样学生GPA数据
     * <p>
     * 包含同专业优秀学长的GPA记录，仅返回当前学期之后的预测学年数据
     * 用于展示目标轨迹，激励预警学生向榜样学习
     */
    @Schema(description = "榜样学生GPA数据", example = "[{\"semester\":\"大二下\",\"gpa\":\"3.8\"},{\"semester\":\"大三上\",\"gpa\":\"3.9\"}]")
    private List<Map<String, String>> exemplaryStudentGpa;

    /**
     * 相似学生GPA数据
     * <p>
     * 包含与预警学生学业水平相似的学长GPA记录，仅返回当前学期之后的预测学年数据
     * 用于展示参考轨迹，提供现实可达的改进目标
     */
    @Schema(description = "相似学生GPA数据", example = "[{\"semester\":\"大二下\",\"gpa\":\"3.2\"},{\"semester\":\"大三上\",\"gpa\":\"3.4\"}]")
    private List<Map<String, String>> similarStudentGpa;
}
