package com.zkdm.iai.domain.entity.library;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：图书馆闸机通行信息 (T_GXTS_TSGZJTXXX)
 * 所属部门: 图书馆
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXTS_TSGZJTXXX")
public class LibraryGateAccessLog {

    /**
     * 唯一标识 (主键)
     */
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学工号 (关联到本科生基本信息表的学号)
     */
    @TableField("XGH")
    private String userNumber;

    /**
     * 姓名 (关联到本科生基本信息表的姓名)
     */
    @TableField("XM")
    private String name;

    /**
     * 人员类型 (关联代码表: DM_GXTS_RYLX)
     */
    @TableField("RYLX")
    private String personType;

    /**
     * 部门名称 (关联到本科生基本信息表的院系)
     */
    @TableField("BMMC")
    private String departmentName;

    /**
     * 通行时间
     */
    @TableField("TXSJ")
    private String accessTime;

    /**
     * 通行类型
     */
    @TableField("TXLX")
    private String accessType;

    /**
     * 进出方向 (关联代码表: DM_GXTS_JCFX)
     */
    @TableField("JCFX")
    private String direction;

    /**
     * 图书馆名称 (关联代码表: DM_GXTS_TSGMC)
     */
    @TableField("TSGMC")
    private String libraryName;

    /**
     * 通行门名称 (关联代码表: DM_GXTS_TXMMC)
     */
    @TableField("TXMMC")
    private String gateName;

    /**
     * 闸机编号 (关联代码表: DM_GXTS_ZJBH)
     */
    @TableField("ZJBH")
    private String gateMachineId;

    /**
     * 时间戳
     */
    @TableField("TTIMESTAMP")
    private String timestamp;
}