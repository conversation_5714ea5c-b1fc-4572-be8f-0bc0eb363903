package com.zkdm.iai.domain.entity.student;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 实体类：本科生奖学金信息 (T_GXXS_BKSJXJXX)
 * 所属部门: 学工处
 * <AUTHOR>
 */
@Schema(description = "本科生奖学金信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXXS_BKSJXJXX")
public class UndergraduateScholarship {

    /**
     * 唯一标识 (主键)
     */
    @Schema(description = "唯一标识", example = "JXJ20230001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableId(value = "WYBS", type = IdType.INPUT)
    private String id;

    /**
     * 学号
     */
    @Schema(description = "学号", example = "202310001", requiredMode = Schema.RequiredMode.REQUIRED)
    @TableField("XH")
    private String studentId;

    /**
     * 姓名
     */
    @Schema(description = "姓名", example = "张三")
    @TableField("XM")
    private String name;

    /**
     * 性别码
     */
    @Schema(description = "性别码", example = "1")
    @TableField("XBM")
    private String genderCode;

    /**
     * 奖学金名称
     */
    @Schema(description = "奖学金名称", example = "国家奖学金")
    @TableField("JXJMC")
    private String scholarshipName;

    /**
     * 奖学金类型码 (关联字典码表DM_XS_JXJLXM)
     */
    @Schema(description = "奖学金类型码 (关联字典码表DM_XS_JXJLXM)", example = "01")
    @TableField("JXJLXM")
    private String scholarshipTypeCode;

    /**
     * 获奖学年
     */
    @Schema(description = "获奖学年", example = "2023-2024")
    @TableField("HJXN")
    private String awardAcademicYear;

    /**
     * 申报时间
     */
    @Schema(description = "申报时间", example = "20231015093000")
    @TableField("SBSJ")
    private String applicationTime;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态", example = "2")
    @TableField("SHZT")
    private String auditStatus;

    /**
     * 院系部编码 (关联组织机构码表DM_VB_ZZ_JGXX)
     */
    @Schema(description = "院系部编码 (关联组织机构码表DM_VB_ZZ_JGXX)", example = "CS001")
    @TableField("YXXBM")
    private String facultyDeptCode;

    /**
     * 学工部编码
     */
    @Schema(description = "学工部编码", example = "XGB01")
    @TableField("XGBLBM")
    private String studentAffairsDeptCode;

    /**
     * 教务部编码
     */
    @Schema(description = "教务部编码", example = "JWB01")
    @TableField("JWHBLBM")
    private String academicAffairsDeptCode;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "20231015093000")
    @TableField("TSTAMP")
    private String timestamp;

}