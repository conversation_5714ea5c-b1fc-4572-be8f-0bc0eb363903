package com.zkdm.iai.domain.entity.library;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 实体类：借阅历史信息 (T_GXTS_JYLSXX)
 * 所属部门: 图书馆
 * <AUTHOR>
 */
@Schema(description = "借阅历史信息")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("T_GXTS_JYLSXX")
public class BorrowingHistoryInfo {

    /**
     * GID (主键)
     */
    @Schema(description = "GID, 唯一标识", example = "a1b2c3d4-e5f6-7890-1234-567890abcdef")
    @TableId(value = "GID", type = IdType.INPUT)
    private String gid;

    /**
     * 学工号（对应学生的学号）
     */
    @Schema(description = "学工号", example = "202310001")
    @TableField("XGH")
    private String studentStaffId;

    /**
     * 借阅时间
     */
    @Schema(description = "借阅时间", example = "2024-09-01 10:30:00")
    @TableField("JYSJ")
    private String borrowTime;

    /**
     * 归还时间
     */
    @Schema(description = "归还时间", example = "2024-09-30 17:00:00")
    @TableField("GHSJ")
    private String returnTime;

    /**
     * 图书馆证件号
     */
    @Schema(description = "图书馆证件号", example = "L202310001")
    @TableField("TSGZJH")
    private String libraryCardNumber;

    /**
     * 图书分类号
     */
    @Schema(description = "图书分类号", example = "TP393.09")
    @TableField("TSFLH")
    private String bookClassificationNumber;

    /**
     * 图书题名
     */
    @Schema(description = "图书题名", example = "人工智能")
    @TableField("TSTM")
    private String bookTitle;

    /**
     * 时间戳
     */
    @Schema(description = "时间戳", example = "毫秒制时间戳")
    @TableField("TSTAMP")
    private String timestamp;

    /**
     * 超期天数
     */
    @Schema(description = "超期天数", example = "0")
    @TableField("CQTS")
    private String overdueDays;
}