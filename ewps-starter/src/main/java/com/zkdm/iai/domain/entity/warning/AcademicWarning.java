package com.zkdm.iai.domain.entity.warning;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 学业预警信息实体类
 * <p>
 * 对应数据库表：TB_ACADEMIC_WARNING
 * 用于存储学生的学业预警信息，包括预警状态、相似学生、榜样学生等信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("TB_ACADEMIC_WARNING")
@Schema(description = "学业预警信息实体")
public class AcademicWarning {

    /**
     * 唯一标识
     */
    @Schema(description = "唯一标识", example = "WARNING001")
    @TableId("WYBS")
    private String id;

    /**
     * 预警学生学号
     */
    @Schema(description = "预警学生学号", example = "202310001")
    @TableField("XH")
    private String studentId;

    /**
     * 预警类型码
     */
    @Schema(description = "预警类型码", example = "01")
    @TableField("WARNING_TYPE")
    private String warningType;

    /**
     * 预警状态码
     */
    @Schema(description = "预警状态码", example = "1")
    @TableField("WARNING_STATUS")
    private Integer warningStatus;

    /**
     * 相似学生学号
     */
    @Schema(description = "算法预测相似同学的学号", example = "202209001")
    @TableField("SIMILARITY_STUDENT_XH")
    private String similarityStudentId;

    /**
     * 榜样学生学号
     */
    @Schema(description = "算法预测榜样同学的学号", example = "202209002")
    @TableField("EXAMPLE_STUDENT_XH")
    private String exampleStudentId;

    /**
     * 榜样差异描述
     */
    @Schema(description = "榜样差异描述", example = "GPA差距0.8分，出勤率差距15%")
    @TableField("EXAMPLE_DIFFERENCES")
    private String exampleDifferences;

    /**
     * 指导建议描述
     */
    @Schema(description = "指导建议描述", example = "建议加强专业课学习，提高课堂出勤率")
    @TableField("GUIDANCE")
    private String guidance;
}
