package com.zkdm.iai.service.impl;

import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import com.zkdm.iai.provider.impl.academicinfo.GpaAnalysisProvider;
import com.zkdm.iai.service.IGpaAnalysisService;
import com.zkdm.iai.util.SemesterDateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * GPA分析服务实现类
 * <p>
 * 实现学生历年GPA信息对比分析功能，主要职责：
 * 1. 参数验证和预处理
 * 2. 时间戳转换为当前学期
 * 3. 调用Provider获取分析数据
 * 4. 异常处理和日志记录
 * <p>
 * 业务流程：
 * 1. 验证输入参数的有效性
 * 2. 根据时间戳确定当前学期
 * 3. 委托Provider执行具体的数据查询和分析
 * 4. 返回格式化的分析结果
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GpaAnalysisServiceImpl implements IGpaAnalysisService {

    private final GpaAnalysisProvider gpaAnalysisProvider;

    /**
     * 获取学生GPA分析对比数据
     * <p>
     * 核心业务逻辑：
     * 1. 验证学生学号的有效性
     * 2. 根据时间戳确定当前学期（如果提供）
     * 3. 调用Provider获取GPA分析数据
     * 4. 处理异常情况并记录日志
     * 
     * @param studentId 预警学生学号，不能为空
     * @param timestamp 当前时间戳，用于确定当前学期，可以为null
     * @return GPA分析对比数据，包含三条GPA轨迹曲线的数据
     * @throws IllegalArgumentException 当学生学号为空时抛出
     */
    @Override
    public GpaAnalysisVO getGpaAnalysis(@NonNull String studentId, Long timestamp) {
        // 1. 参数验证
        if (!StringUtils.hasText(studentId)) {
            throw new IllegalArgumentException("学生学号不能为空");
        }

        try {
            log.info("开始获取学生{}的GPA分析数据，时间戳：{}", studentId, timestamp);

            // 2. 确定当前学期
            String currentSemester = null;
            if (timestamp != null) {
                currentSemester = SemesterDateUtil.getCurrentSemesterByTimestamp(timestamp);
                log.debug("根据时间戳{}确定当前学期：{}", timestamp, currentSemester);
            } else {
                // 如果没有提供时间戳，使用当前时间
                currentSemester = SemesterDateUtil.getCurrentSemester();
                log.debug("使用当前时间确定学期：{}", currentSemester);
            }

            // 3. 调用Provider获取分析数据
            GpaAnalysisVO result = gpaAnalysisProvider.getGpaAnalysis(studentId, currentSemester);

            log.info("学生{}的GPA分析数据获取成功，当前学期：{}", studentId, currentSemester);
            logAnalysisResult(result);

            return result;

        } catch (IllegalArgumentException e) {
            log.warn("获取GPA分析数据失败，参数错误：{}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("获取学生{}的GPA分析数据时发生异常，时间戳：{}", studentId, timestamp, e);
            
            // 返回空数据而不是抛出异常，确保接口稳定性
            return GpaAnalysisVO.builder()
                    .warningStudentGpa(java.util.Collections.emptyList())
                    .exemplaryStudentGpa(java.util.Collections.emptyList())
                    .similarStudentGpa(java.util.Collections.emptyList())
                    .build();
        }
    }

    /**
     * 记录分析结果的统计信息
     * <p>
     * 用于调试和监控，记录各类数据的数量
     * 
     * @param result GPA分析结果
     */
    private void logAnalysisResult(GpaAnalysisVO result) {
        if (result == null) {
            log.warn("GPA分析结果为null");
            return;
        }

        int warningCount = result.getWarningStudentGpa() != null ? result.getWarningStudentGpa().size() : 0;
        int exemplaryCount = result.getExemplaryStudentGpa() != null ? result.getExemplaryStudentGpa().size() : 0;
        int similarCount = result.getSimilarStudentGpa() != null ? result.getSimilarStudentGpa().size() : 0;

        log.debug("GPA分析结果统计 - 预警学生数据：{}条，榜样学生数据：{}条，相似学生数据：{}条", 
                warningCount, exemplaryCount, similarCount);

        // 记录具体的学期范围（仅在debug级别）
        if (log.isDebugEnabled()) {
            logSemesterRange("预警学生", result.getWarningStudentGpa());
            logSemesterRange("榜样学生", result.getExemplaryStudentGpa());
            logSemesterRange("相似学生", result.getSimilarStudentGpa());
        }
    }

    /**
     * 记录学期范围信息
     * <p>
     * 用于调试，记录数据的学期范围
     * 
     * @param studentType 学生类型描述
     * @param gpaData GPA数据列表
     */
    private void logSemesterRange(String studentType, java.util.List<java.util.Map<String, String>> gpaData) {
        if (gpaData == null || gpaData.isEmpty()) {
            log.debug("{}GPA数据为空", studentType);
            return;
        }

        String firstSemester = gpaData.get(0).get("semester");
        String lastSemester = gpaData.get(gpaData.size() - 1).get("semester");
        
        log.debug("{}GPA数据学期范围：{} 到 {}", studentType, firstSemester, lastSemester);
    }
}
