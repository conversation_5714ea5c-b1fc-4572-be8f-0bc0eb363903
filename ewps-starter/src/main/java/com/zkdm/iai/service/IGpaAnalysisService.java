package com.zkdm.iai.service;

import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import org.springframework.lang.NonNull;

/**
 * GPA分析服务接口
 * <p>
 * 提供学生历年GPA信息对比分析功能，包括：
 * - 预警学生的历史GPA轨迹分析
 * - 榜样学生的GPA参考轨迹
 * - 相似学生的GPA参考轨迹
 * <p>
 * 主要用于学业预警系统中的GPA趋势对比分析
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public interface IGpaAnalysisService {

    /**
     * 获取学生GPA分析对比数据
     * <p>
     * 根据学生学号和时间戳，提供完整的GPA对比分析数据：
     * 1. 预警学生从入学到当前学期的历史GPA数据
     * 2. 榜样学生当前学期之后的GPA轨迹（目标参考）
     * 3. 相似学生当前学期之后的GPA轨迹（现实参考）
     * 
     * @param studentId 预警学生学号，不能为空
     * @param timestamp 当前时间戳，用于确定当前学期，可以为null
     * @return GPA分析对比数据，包含三条GPA轨迹曲线的数据
     * @throws IllegalArgumentException 当学生学号为空时抛出
     */
    GpaAnalysisVO getGpaAnalysis(@NonNull String studentId, Long timestamp);
}
