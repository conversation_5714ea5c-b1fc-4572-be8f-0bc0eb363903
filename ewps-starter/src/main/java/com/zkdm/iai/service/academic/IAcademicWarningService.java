package com.zkdm.iai.service.academic;

import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

/**
 * Service接口: 学业预警相关功能
 */
public interface IAcademicWarningService {
    /**
     * 获取指定学生和学期的详细学业指标。
     *
     * @param studentId 学生的唯一ID（学号）。
     * @param semester 学期（可选），格式如：2023-2024-1
     * @return 包含聚合后学业数据的 {@link AcademicInfoVO} 对象。
     */
    AcademicInfoVO getAcademicInfo(@NonNull String studentId, @Nullable String semester);
}