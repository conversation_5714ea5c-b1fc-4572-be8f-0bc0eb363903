package com.zkdm.iai.service.impl.academic;

import com.zkdm.iai.domain.vo.academic.AcademicInfoVO;
import com.zkdm.iai.provider.AcademicInfoProvider;
import com.zkdm.iai.service.academic.IAcademicWarningService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * Service实现类: 学业预警功能。
 * <p>
 * [优化] 此类通过注入一个Provider列表来实现完全的开闭原则。
 * 当新增学业指标时，无需修改此文件。
 */
@Service
@RequiredArgsConstructor
public class AcademicWarningServiceImpl implements IAcademicWarningService {

    // 注入所有实现了AcademicInfoProvider接口的Bean列表,Spring会自动装配。
    private final List<AcademicInfoProvider> providers;

    /**
     * 任务编排（支持学期参数）
     * @param studentId 学生的唯一ID（学号）。
     * @param semester 学期（可选），格式如：2023-2024-1
     * @return AcademicInfoVO
     */
    public AcademicInfoVO getAcademicInfo(String studentId, String semester) {

        // 并行执行所有provider，每个provider返回一个Consumer函数。
        // 这个过程是线程安全的，因为每个任务都独立工作，不共享状态。
        List<CompletableFuture<Consumer<AcademicInfoVO.AcademicInfoVOBuilder>>> futures = providers.stream()
                .map(provider -> CompletableFuture.supplyAsync(() -> provider.provide(studentId, semester)))
                .collect(Collectors.toList());

        // 等待所有异步任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 在主线程中安全地构建VO
        var builder = AcademicInfoVO.builder();

        // 串行执行所有Consumer函数来配置builder，避免了多线程修改同一个Builder实例的风险。
        futures.forEach(future -> {
            try {
                // 调用 .get() 或 .join() 获取已完成的Consumer
                future.join().accept(builder);
            } catch (Exception e) {
                // 可以添加日志记录等异常处理
                // 此处简单忽略，或可以抛出自定义异常
            }
        });

        return builder.build();
    }
}