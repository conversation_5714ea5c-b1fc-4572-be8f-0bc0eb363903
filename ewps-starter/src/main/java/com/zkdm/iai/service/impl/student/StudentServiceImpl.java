package com.zkdm.iai.service.impl.student;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zkdm.iai.domain.entity.student.UndergraduateBasicInfo;

import com.zkdm.iai.domain.vo.student.ProfileVo;
import com.zkdm.iai.domain.vo.student.StudentListVo;
import com.zkdm.iai.domain.vo.student.StudentOverviewVo;
import com.zkdm.iai.domain.vo.student.WarningModuleVo;
import com.zkdm.iai.mapper.student.StudentMapper;
import com.zkdm.iai.service.student.IStudentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class StudentServiceImpl implements IStudentService {

    private final StudentMapper studentMapper;

    @Override
    public IPage<StudentListVo> getStudentList(Page<UndergraduateBasicInfo> page, String query) {
        // 使用XML中的复杂查询，一次性获取学生列表和预警次数
        return studentMapper.getStudentListWithWarningCount(page, query);
    }

    @Override
    public StudentOverviewVo getStudentOverview(String studentId) {
        // 使用XML中的复杂查询获取学生档案信息
        ProfileVo profile = studentMapper.getStudentOverview(studentId);
        if (profile == null) {
            throw new IllegalArgumentException("未找到学号为 " + studentId + " 的学生");
        }

        // 获取预警模块信息
        List<WarningModuleVo> warningModules = studentMapper.getStudentWarningModules(studentId);

        // 组装最终的概览VO
        StudentOverviewVo overview = new StudentOverviewVo();
        overview.setProfile(profile);
        overview.setWarningModules(warningModules);

        return overview;
    }


}