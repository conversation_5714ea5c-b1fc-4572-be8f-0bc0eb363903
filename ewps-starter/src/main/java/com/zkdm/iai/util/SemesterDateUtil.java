package com.zkdm.iai.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

/**
 * 学期日期工具类
 * 用于处理学期格式与日期范围的转换，支持时间戳转学期功能
 *
 * <AUTHOR>
 */
@Slf4j
public class SemesterDateUtil {
    
    /**
     * 根据学期格式获取学期开始日期
     * 
     * @param semester 学期格式，如：2023-2024-1
     * @return 学期开始日期，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String getSemesterStartDate(String semester) {
        if (!StringUtils.hasText(semester)) {
            return null;
        }
        
        try {
            String[] parts = semester.split("-");
            if (parts.length != 3) {
                log.warn("学期格式不正确：{}", semester);
                return null;
            }
            
            String startYear = parts[0];
            String semesterNumber = parts[2];
            
            // 第一学期：9月1日开始，第二学期：2月1日开始
            if ("1".equals(semesterNumber)) {
                return startYear + "-09-01 00:00:00";
            } else if ("2".equals(semesterNumber)) {
                String endYear = parts[1];
                return endYear + "-02-01 00:00:00";
            } else {
                log.warn("不支持的学期编号：{}", semesterNumber);
                return null;
            }
        } catch (Exception e) {
            log.error("解析学期开始日期失败，学期：{}", semester, e);
            return null;
        }
    }
    
    /**
     * 根据学期格式获取学期结束日期
     * 
     * @param semester 学期格式，如：2023-2024-1
     * @return 学期结束日期，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String getSemesterEndDate(String semester) {
        if (!StringUtils.hasText(semester)) {
            return null;
        }
        
        try {
            String[] parts = semester.split("-");
            if (parts.length != 3) {
                log.warn("学期格式不正确：{}", semester);
                return null;
            }
            
            String startYear = parts[0];
            String endYear = parts[1];
            String semesterNumber = parts[2];
            
            // 第一学期：1月31日结束，第二学期：8月31日结束
            if ("1".equals(semesterNumber)) {
                return endYear + "-01-31 23:59:59";
            } else if ("2".equals(semesterNumber)) {
                return endYear + "-08-31 23:59:59";
            } else {
                log.warn("不支持的学期编号：{}", semesterNumber);
                return null;
            }
        } catch (Exception e) {
            log.error("解析学期结束日期失败，学期：{}", semester, e);
            return null;
        }
    }
    
    /**
     * 从学期格式中提取学年
     * 例如：2023-2024-1 -> 2023-2024
     * 
     * @param semester 学期格式
     * @return 学年
     */
    public static String extractAcademicYear(String semester) {
        if (!StringUtils.hasText(semester)) {
            return null;
        }
        
        String[] parts = semester.split("-");
        if (parts.length >= 2) {
            return parts[0] + "-" + parts[1];
        }
        
        return null;
    }
    
    /**
     * 获取上一个学期
     * 例如：2023-2024-2 -> 2023-2024-1
     *      2023-2024-1 -> 2022-2023-2
     * 
     * @param currentSemester 当前学期
     * @return 上一个学期
     */
    public static String getPreviousSemester(String currentSemester) {
        if (!StringUtils.hasText(currentSemester)) {
            return null;
        }
        
        try {
            String[] parts = currentSemester.split("-");
            if (parts.length != 3) {
                return null;
            }
            
            int startYear = Integer.parseInt(parts[0]);
            int endYear = Integer.parseInt(parts[1]);
            int semesterNumber = Integer.parseInt(parts[2]);
            
            if (semesterNumber == 2) {
                // 第二学期的上一学期是第一学期
                return startYear + "-" + endYear + "-1";
            } else if (semesterNumber == 1) {
                // 第一学期的上一学期是上一学年的第二学期
                return (startYear - 1) + "-" + (endYear - 1) + "-2";
            }
            
            return null;
        } catch (Exception e) {
            log.error("计算上一学期失败，当前学期：{}", currentSemester, e);
            return null;
        }
    }

    /**
     * 根据时间戳判断当前学期
     * <p>
     * 学期划分规则：
     * - 第一学期：9月1日 - 1月31日
     * - 第二学期：2月1日 - 8月31日
     *
     * @param timestamp 时间戳（毫秒）
     * @return 学期格式，如：2023-2024-1，解析失败返回null
     */
    public static String getCurrentSemesterByTimestamp(long timestamp) {
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp),
                ZoneId.systemDefault()
            );

            return getCurrentSemesterByDateTime(dateTime);
        } catch (Exception e) {
            log.error("根据时间戳{}解析学期失败", timestamp, e);
            return null;
        }
    }

    /**
     * 根据LocalDateTime判断当前学期
     * <p>
     * 学期划分规则：
     * - 第一学期：9月1日 - 1月31日（跨年）
     * - 第二学期：2月1日 - 8月31日
     *
     * @param dateTime 日期时间对象
     * @return 学期格式，如：2023-2024-1，解析失败返回null
     */
    public static String getCurrentSemesterByDateTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }

        try {
            int year = dateTime.getYear();
            int month = dateTime.getMonthValue();

            if (month >= 9 && month <= 12) {
                // 9-12月：第一学期，学年为当前年-下一年
                return year + "-" + (year + 1) + "-1";
            } else if (month >= 2 && month <= 8) {
                // 2-8月：第二学期，学年为上一年-当前年
                return (year - 1) + "-" + year + "-2";
            } else if (month == 1) {
                // 1月：第一学期的后半段，学年为上一年-当前年
                return (year - 1) + "-" + year + "-1";
            }

            log.warn("无法确定月份{}对应的学期", month);
            return null;
        } catch (Exception e) {
            log.error("根据日期时间{}解析学期失败", dateTime, e);
            return null;
        }
    }

    /**
     * 获取当前时间对应的学期
     *
     * @return 当前学期格式，如：2023-2024-1
     */
    public static String getCurrentSemester() {
        return getCurrentSemesterByDateTime(LocalDateTime.now());
    }

    /**
     * 验证学期格式是否正确
     *
     * @param semester 学期字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidSemesterFormat(String semester) {
        if (!StringUtils.hasText(semester)) {
            return false;
        }

        // 正则表达式：YYYY-YYYY-[1|2]
        return semester.matches("\\d{4}-\\d{4}-[12]");
    }

    /**
     * 将学期格式转换为中文描述
     * <p>
     * 转换规则：
     * - 根据入学年份计算年级
     * - 第1学期转换为"上"，第2学期转换为"下"
     * - 示例：2023-2024-1 → 大一上（假设2023年入学）
     *
     * @param semester 学期格式，如：2023-2024-1
     * @param enrollmentYear 入学年份，如：2023
     * @return 中文学期描述，如："大一上"、"大二下"等，解析失败返回原始学期字符串
     */
    public static String convertSemesterToChineseDescription(String semester, int enrollmentYear) {
        if (!StringUtils.hasText(semester) || !isValidSemesterFormat(semester)) {
            return semester;
        }

        try {
            String[] parts = semester.split("-");
            int startYear = Integer.parseInt(parts[0]);
            String semesterNumber = parts[2];

            // 计算年级（从入学年份开始计算）
            int grade = startYear - enrollmentYear + 1;

            // 转换年级为中文
            String gradeStr;
            switch (grade) {
                case 1 -> gradeStr = "大一";
                case 2 -> gradeStr = "大二";
                case 3 -> gradeStr = "大三";
                case 4 -> gradeStr = "大四";
                case 5 -> gradeStr = "大五";
                default -> gradeStr = "大" + grade;
            }

            // 转换学期为中文
            String semesterStr = "1".equals(semesterNumber) ? "上" : "下";

            return gradeStr + semesterStr;
        } catch (Exception e) {
            log.error("转换学期{}为中文描述失败，入学年份：{}", semester, enrollmentYear, e);
            return semester;
        }
    }

    /**
     * 根据学号推断入学年份
     * <p>
     * 学号格式假设：前4位为入学年份
     * 示例：202310001 → 2023年入学
     *
     * @param studentId 学生学号
     * @return 入学年份，解析失败返回当前年份
     */
    public static int extractEnrollmentYearFromStudentId(String studentId) {
        if (!StringUtils.hasText(studentId) || studentId.length() < 4) {
            return LocalDateTime.now().getYear();
        }

        try {
            return Integer.parseInt(studentId.substring(0, 4));
        } catch (NumberFormatException e) {
            log.warn("从学号{}中提取入学年份失败", studentId, e);
            return LocalDateTime.now().getYear();
        }
    }

    /**
     * 将学期格式转换为中文描述（自动推断入学年份）
     * <p>
     * 根据学号自动推断入学年份，然后转换学期格式
     *
     * @param semester 学期格式，如：2023-2024-1
     * @param studentId 学生学号，用于推断入学年份
     * @return 中文学期描述，如："大一上"、"大二下"等
     */
    public static String convertSemesterToChineseDescription(String semester, String studentId) {
        int enrollmentYear = extractEnrollmentYearFromStudentId(studentId);
        return convertSemesterToChineseDescription(semester, enrollmentYear);
    }

    /**
     * 获取指定学期之后的所有学期列表
     * <p>
     * 用于获取预测学期数据，通常用于展示榜样学生和相似学生的未来轨迹
     *
     * @param currentSemester 当前学期，如：2023-2024-1
     * @param count 需要获取的学期数量
     * @return 后续学期列表，如：[2023-2024-2, 2024-2025-1, 2024-2025-2]
     */
    public static java.util.List<String> getFollowingSemesters(String currentSemester, int count) {
        java.util.List<String> semesters = new java.util.ArrayList<>();

        if (!StringUtils.hasText(currentSemester) || !isValidSemesterFormat(currentSemester) || count <= 0) {
            return semesters;
        }

        try {
            String[] parts = currentSemester.split("-");
            int startYear = Integer.parseInt(parts[0]);
            int endYear = Integer.parseInt(parts[1]);
            int semesterNumber = Integer.parseInt(parts[2]);

            for (int i = 0; i < count; i++) {
                // 计算下一个学期
                if (semesterNumber == 1) {
                    // 第一学期的下一个是第二学期
                    semesterNumber = 2;
                } else {
                    // 第二学期的下一个是下一学年的第一学期
                    semesterNumber = 1;
                    startYear++;
                    endYear++;
                }

                String nextSemester = startYear + "-" + endYear + "-" + semesterNumber;
                semesters.add(nextSemester);
            }
        } catch (Exception e) {
            log.error("获取学期{}之后的{}个学期失败", currentSemester, count, e);
        }

        return semesters;
    }
}
