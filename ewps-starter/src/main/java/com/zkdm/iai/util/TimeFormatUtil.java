package com.zkdm.iai.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;

/**
 * 时间格式转换工具类
 * 用于处理数据库中字符串格式的时间字段
 * 
 * <AUTHOR>
 */
@Slf4j
public class TimeFormatUtil {
    
    /**
     * 数据库时间格式：YYYYMMDDHHMMSS
     */
    private static final DateTimeFormatter DB_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
    
    /**
     * 标准时间格式：yyyy-MM-dd HH:mm:ss
     */
    private static final DateTimeFormatter STANDARD_TIME_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 将数据库时间字符串转换为LocalDateTime
     * 
     * @param dbTimeStr 数据库时间字符串，格式：YYYYMMDDHHMMSS
     * @return LocalDateTime对象，转换失败返回null
     */
    public static LocalDateTime parseDbTime(String dbTimeStr) {
        if (!StringUtils.hasText(dbTimeStr)) {
            return null;
        }
        
        try {
            return LocalDateTime.parse(dbTimeStr, DB_TIME_FORMAT);
        } catch (DateTimeParseException e) {
            log.warn("解析数据库时间字符串失败：{}", dbTimeStr, e);
            return null;
        }
    }
    
    /**
     * 将LocalDateTime转换为数据库时间字符串
     * 
     * @param dateTime LocalDateTime对象
     * @return 数据库时间字符串，格式：YYYYMMDDHHMMSS
     */
    public static String formatToDbTime(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        
        try {
            return dateTime.format(DB_TIME_FORMAT);
        } catch (Exception e) {
            log.warn("格式化时间为数据库格式失败：{}", dateTime, e);
            return null;
        }
    }
    
    /**
     * 将数据库时间字符串转换为标准格式字符串
     * 
     * @param dbTimeStr 数据库时间字符串，格式：YYYYMMDDHHMMSS
     * @return 标准格式时间字符串，格式：yyyy-MM-dd HH:mm:ss
     */
    public static String formatDbTimeToStandard(String dbTimeStr) {
        LocalDateTime dateTime = parseDbTime(dbTimeStr);
        if (dateTime == null) {
            return null;
        }
        
        try {
            return dateTime.format(STANDARD_TIME_FORMAT);
        } catch (Exception e) {
            log.warn("格式化时间为标准格式失败：{}", dateTime, e);
            return null;
        }
    }
    
    /**
     * 将标准格式时间字符串转换为数据库格式
     * 
     * @param standardTimeStr 标准格式时间字符串，格式：yyyy-MM-dd HH:mm:ss
     * @return 数据库时间字符串，格式：YYYYMMDDHHMMSS
     */
    public static String formatStandardToDbTime(String standardTimeStr) {
        if (!StringUtils.hasText(standardTimeStr)) {
            return null;
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.parse(standardTimeStr, STANDARD_TIME_FORMAT);
            return formatToDbTime(dateTime);
        } catch (DateTimeParseException e) {
            log.warn("解析标准时间字符串失败：{}", standardTimeStr, e);
            return null;
        }
    }
    
    /**
     * 验证数据库时间字符串格式是否正确
     * 
     * @param dbTimeStr 数据库时间字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidDbTimeFormat(String dbTimeStr) {
        if (!StringUtils.hasText(dbTimeStr)) {
            return false;
        }
        
        // 检查长度
        if (dbTimeStr.length() != 14) {
            return false;
        }
        
        // 检查是否全为数字
        if (!dbTimeStr.matches("\\d{14}")) {
            return false;
        }
        
        // 尝试解析
        return parseDbTime(dbTimeStr) != null;
    }
    
    /**
     * 获取当前时间的数据库格式字符串
     * 
     * @return 当前时间的数据库格式字符串
     */
    public static String getCurrentDbTime() {
        return formatToDbTime(LocalDateTime.now());
    }
    
    /**
     * 比较两个数据库时间字符串的先后顺序
     * 
     * @param dbTime1 第一个时间字符串
     * @param dbTime2 第二个时间字符串
     * @return 负数表示dbTime1早于dbTime2，0表示相等，正数表示dbTime1晚于dbTime2
     */
    public static int compareDbTime(String dbTime1, String dbTime2) {
        LocalDateTime dateTime1 = parseDbTime(dbTime1);
        LocalDateTime dateTime2 = parseDbTime(dbTime2);
        
        if (dateTime1 == null && dateTime2 == null) {
            return 0;
        }
        if (dateTime1 == null) {
            return -1;
        }
        if (dateTime2 == null) {
            return 1;
        }
        
        return dateTime1.compareTo(dateTime2);
    }
}
