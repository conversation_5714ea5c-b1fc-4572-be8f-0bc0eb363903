<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zkdm.iai.mapper.warning.AcademicWarningMapper">

    <!-- 通用结果映射 -->
    <resultMap id="BaseResultMap" type="com.zkdm.iai.domain.entity.warning.AcademicWarning">
        <id column="WYBS" property="id" />
        <result column="XH" property="studentId" />
        <result column="WARNING_TYPE" property="warningType" />
        <result column="WARNING_STATUS" property="warningStatus" />
        <result column="SIMILARITY_STUDENT_XH" property="similarityStudentId" />
        <result column="EXAMPLE_STUDENT_XH" property="exampleStudentId" />
        <result column="EXAMPLE_DIFFERENCES" property="exampleDifferences" />
        <result column="GUIDANCE" property="guidance" />
    </resultMap>

    <!-- 根据学生学号查询有效的学业预警信息 -->
    <select id="selectActiveWarningByStudentId" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            WARNING_TYPE,
            WARNING_STATUS,
            SIMILARITY_STUDENT_XH,
            EXAMPLE_STUDENT_XH,
            EXAMPLE_DIFFERENCES,
            GUIDANCE
        FROM TB_ACADEMIC_WARNING 
        WHERE XH = #{studentId} 
        AND WARNING_TYPE = '01' 
        AND WARNING_STATUS = 1
        LIMIT 1
    </select>

    <!-- 根据学生学号查询最新的学业预警信息 -->
    <select id="selectLatestWarningByStudentId" resultMap="BaseResultMap">
        SELECT 
            WYBS,
            XH,
            WARNING_TYPE,
            WARNING_STATUS,
            SIMILARITY_STUDENT_XH,
            EXAMPLE_STUDENT_XH,
            EXAMPLE_DIFFERENCES,
            GUIDANCE
        FROM TB_ACADEMIC_WARNING 
        WHERE XH = #{studentId} 
        AND WARNING_TYPE = '01'
        ORDER BY WYBS DESC
        LIMIT 1
    </select>

</mapper>
