package com.zkdm.iai.provider.impl.academicinfo;

import com.zkdm.iai.domain.vo.academic.GpaAnalysisVO;
import com.zkdm.iai.provider.impl.academicinfo.enums.StudentType;
import com.zkdm.iai.provider.impl.academicinfo.factory.GpaProviderFactory;
import com.zkdm.iai.provider.impl.academicinfo.strategy.GpaDataStrategy;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.jdbc.Sql;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GPA分析Provider测试类
 * <p>
 * 测试重构后的GPA分析功能，验证：
 * - 策略模式的正确实现
 * - 工厂模式的正确工作
 * - 不同类型学生数据的正确获取
 * - 异常情况的正确处理
 * - 数据转换的正确性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
@Transactional
@DisplayName("GPA分析Provider测试")
class GpaAnalysisProviderTest {
    
    @Autowired
    private GpaAnalysisProvider gpaAnalysisProvider;
    
    @Autowired
    private GpaProviderFactory gpaProviderFactory;
    
    /**
     * 测试策略工厂的初始化
     */
    @Test
    @DisplayName("测试策略工厂初始化")
    void testStrategyFactoryInitialization() {
        // 验证所有策略都已注册
        assertTrue(gpaProviderFactory.supportsStudentType(StudentType.WARNING));
        assertTrue(gpaProviderFactory.supportsStudentType(StudentType.EXEMPLARY));
        assertTrue(gpaProviderFactory.supportsStudentType(StudentType.SIMILAR));
        
        // 验证策略获取
        GpaDataStrategy warningStrategy = gpaProviderFactory.getStrategy(StudentType.WARNING);
        GpaDataStrategy exemplaryStrategy = gpaProviderFactory.getStrategy(StudentType.EXEMPLARY);
        GpaDataStrategy similarStrategy = gpaProviderFactory.getStrategy(StudentType.SIMILAR);
        
        assertNotNull(warningStrategy);
        assertNotNull(exemplaryStrategy);
        assertNotNull(similarStrategy);
        
        // 验证策略类型
        assertEquals(StudentType.WARNING, warningStrategy.getStudentType());
        assertEquals(StudentType.EXEMPLARY, exemplaryStrategy.getStudentType());
        assertEquals(StudentType.SIMILAR, similarStrategy.getStudentType());
        
        log.info("策略工厂初始化测试通过");
    }
    
    /**
     * 测试完整的GPA分析流程
     */
    @Test
    @DisplayName("测试完整GPA分析流程")
    @Sql("/sql/gpa_analysis_test_data.sql")
    void testCompleteGpaAnalysis() {
        // 测试学业下滑型预警学生
        String studentId = "202310001";
        String currentSemester = "2024-2025-1";
        
        GpaAnalysisVO result = gpaAnalysisProvider.getGpaAnalysis(studentId, currentSemester);
        
        assertNotNull(result);
        assertNotNull(result.getWarningStudentGpa());
        assertNotNull(result.getExemplaryStudentGpa());
        assertNotNull(result.getSimilarStudentGpa());
        
        // 验证预警学生数据（应该包含历史数据）
        List<Map<String, String>> warningData = result.getWarningStudentGpa();
        assertFalse(warningData.isEmpty());
        assertTrue(warningData.size() >= 3); // 至少有3个学期的数据
        
        // 验证数据格式
        for (Map<String, String> data : warningData) {
            assertTrue(data.containsKey("semester"));
            assertTrue(data.containsKey("gpa"));
            assertNotNull(data.get("semester"));
            assertNotNull(data.get("gpa"));
        }
        
        // 验证榜样学生数据（应该包含未来轨迹）
        List<Map<String, String>> exemplaryData = result.getExemplaryStudentGpa();
        // 榜样学生的数据应该是当前学期之后的，可能为空（如果榜样学生已毕业）
        
        // 验证相似学生数据
        List<Map<String, String>> similarData = result.getSimilarStudentGpa();
        // 相似学生的数据应该是当前学期之后的，可能为空
        
        log.info("完整GPA分析测试通过 - 预警学生数据：{}条，榜样学生数据：{}条，相似学生数据：{}条", 
                warningData.size(), exemplaryData.size(), similarData.size());
    }
    
    /**
     * 测试不同类型的预警学生
     */
    @Test
    @DisplayName("测试不同类型预警学生")
    @Sql("/sql/gpa_analysis_test_data.sql")
    void testDifferentWarningStudentTypes() {
        String currentSemester = "2024-2025-1";
        
        // 测试稳定低分型
        GpaAnalysisVO result1 = gpaAnalysisProvider.getGpaAnalysis("202310002", currentSemester);
        assertNotNull(result1);
        assertFalse(result1.getWarningStudentGpa().isEmpty());
        
        // 测试波动型
        GpaAnalysisVO result2 = gpaAnalysisProvider.getGpaAnalysis("202310003", currentSemester);
        assertNotNull(result2);
        assertFalse(result2.getWarningStudentGpa().isEmpty());
        
        // 测试严重预警型
        GpaAnalysisVO result3 = gpaAnalysisProvider.getGpaAnalysis("202310004", currentSemester);
        assertNotNull(result3);
        assertFalse(result3.getWarningStudentGpa().isEmpty());
        
        // 测试边缘预警型
        GpaAnalysisVO result4 = gpaAnalysisProvider.getGpaAnalysis("202310005", currentSemester);
        assertNotNull(result4);
        assertFalse(result4.getWarningStudentGpa().isEmpty());
        
        log.info("不同类型预警学生测试通过");
    }
    
    /**
     * 测试异常情况处理
     */
    @Test
    @DisplayName("测试异常情况处理")
    void testExceptionHandling() {
        // 测试空学号
        assertThrows(IllegalArgumentException.class, () -> {
            gpaAnalysisProvider.getGpaAnalysis(null, "2024-2025-1");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            gpaAnalysisProvider.getGpaAnalysis("", "2024-2025-1");
        });
        
        assertThrows(IllegalArgumentException.class, () -> {
            gpaAnalysisProvider.getGpaAnalysis("   ", "2024-2025-1");
        });
        
        // 测试不存在的学生（应该返回空数据而不是异常）
        GpaAnalysisVO result = gpaAnalysisProvider.getGpaAnalysis("999999999", "2024-2025-1");
        assertNotNull(result);
        // 预警学生数据应该为空，因为学生不存在
        assertTrue(result.getWarningStudentGpa().isEmpty());
        
        log.info("异常情况处理测试通过");
    }
    
    /**
     * 测试无当前学期的情况
     */
    @Test
    @DisplayName("测试无当前学期情况")
    @Sql("/sql/gpa_analysis_test_data.sql")
    void testWithoutCurrentSemester() {
        String studentId = "202310001";
        
        // 不指定当前学期
        GpaAnalysisVO result = gpaAnalysisProvider.getGpaAnalysis(studentId, null);
        
        assertNotNull(result);
        assertNotNull(result.getWarningStudentGpa());
        
        // 预警学生应该返回所有历史数据
        List<Map<String, String>> warningData = result.getWarningStudentGpa();
        assertFalse(warningData.isEmpty());
        
        log.info("无当前学期情况测试通过 - 返回{}条历史数据", warningData.size());
    }
    
    /**
     * 测试数据格式正确性
     */
    @Test
    @DisplayName("测试数据格式正确性")
    @Sql("/sql/gpa_analysis_test_data.sql")
    void testDataFormat() {
        String studentId = "202310001";
        String currentSemester = "2024-2025-1";
        
        GpaAnalysisVO result = gpaAnalysisProvider.getGpaAnalysis(studentId, currentSemester);
        
        // 验证预警学生数据格式
        for (Map<String, String> data : result.getWarningStudentGpa()) {
            String semester = data.get("semester");
            String gpa = data.get("gpa");
            
            // 验证学期格式（应该是中文描述，如"大一上"）
            assertNotNull(semester);
            assertTrue(semester.matches("大[一二三四五]上|大[一二三四五]下"));
            
            // 验证GPA格式（应该是数字，保留1位小数）
            assertNotNull(gpa);
            assertTrue(gpa.matches("\\d+\\.\\d"));
            
            // 验证GPA值范围（0.0-4.0）
            double gpaValue = Double.parseDouble(gpa);
            assertTrue(gpaValue >= 0.0 && gpaValue <= 4.0);
        }
        
        log.info("数据格式正确性测试通过");
    }
    
    /**
     * 测试策略模式的扩展性
     */
    @Test
    @DisplayName("测试策略模式扩展性")
    void testStrategyPatternExtensibility() {
        // 验证工厂支持的学生类型
        assertEquals(3, gpaProviderFactory.getSupportedStudentTypes().size());
        
        // 验证每种类型都有对应的策略
        for (StudentType type : StudentType.values()) {
            assertTrue(gpaProviderFactory.supportsStudentType(type));
            GpaDataStrategy strategy = gpaProviderFactory.getStrategy(type);
            assertNotNull(strategy);
            assertEquals(type, strategy.getStudentType());
        }
        
        log.info("策略模式扩展性测试通过");
    }
}
